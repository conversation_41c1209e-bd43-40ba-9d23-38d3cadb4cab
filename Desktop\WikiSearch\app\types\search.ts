/**
 * Represents a Wikipedia search result item
 */
export interface WikiSearchResult {
  /** The page ID in Wikipedia */
  pageid: number;
  /** The title of the Wikipedia article */
  title: string;
  /** A snippet or excerpt from the article */
  snippet: string;
  /** The full URL to the Wikipedia article */
  url: string;
}

/**
 * Represents the complete search response
 */
export interface SearchResponse {
  /** Array of search result items */
  results: WikiSearchResult[];
  /** Total number of results found */
  totalResults: number;
  /** The search query that was used */
  query: string;
}

/**
 * Represents the parameters for a search request
 */
export interface SearchParams {
  /** The search query string */
  query: string;
  /** Number of results to return (default: 10) */
  limit?: number;
  /** Result offset for pagination */
  offset?: number;
}