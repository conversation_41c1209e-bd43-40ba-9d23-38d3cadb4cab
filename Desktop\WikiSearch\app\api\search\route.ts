import { NextRequest, NextResponse } from "next/server";
import type { WikiSearchResult } from "@/app/lib/api";

// Define SearchResponse type locally since it's only used in this API route
interface SearchResponse {
  results: WikiSearchResult[];
  totalResults: number;
  query: string;
}

/**
 * API route handler for Wikipedia search
 * @param request - The incoming request object
 * @returns A response with search results
 */
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get("query");
    const limit = parseInt(searchParams.get("limit") || "10", 10);

    if (!query) {
      return NextResponse.json(
        { error: "Search query is required" },
        { status: 400 }
      );
    }

    // Construct the Wikipedia API URL
    const apiUrl = new URL("https://en.wikipedia.org/w/api.php");
    apiUrl.searchParams.append("action", "query");
    apiUrl.searchParams.append("list", "search");
    apiUrl.searchParams.append("srsearch", query);
    apiUrl.searchParams.append("format", "json");
    apiUrl.searchParams.append("srlimit", limit.toString());
    apiUrl.searchParams.append("srprop", "snippet");
    apiUrl.searchParams.append("origin", "*");

    const response = await fetch(apiUrl.toString());

    if (!response.ok) {
      throw new Error(`Wikipedia API error: ${response.statusText}`);
    }

    const data = await response.json();

    // Transform the Wikipedia API response to our format
    const results: WikiSearchResult[] = data.query.search.map((item: any) => ({
      pageid: item.pageid,
      title: item.title,
      snippet: item.snippet.replace(/<\/?span[^>]*>/g, ""), // Remove span tags from snippets
      url: `https://en.wikipedia.org/wiki/${encodeURIComponent(item.title.replace(/ /g, "_"))}`,
    }));

    const searchResponse: SearchResponse = {
      results,
      totalResults: data.query.searchinfo.totalhits,
      query,
    };

    return NextResponse.json(searchResponse);
  } catch (error) {
    console.error("Search API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch search results" },
      { status: 500 }
    );
  }
}