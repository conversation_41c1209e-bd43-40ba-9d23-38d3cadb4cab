"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/groq-sdk";
exports.ids = ["vendor-chunks/groq-sdk"];
exports.modules = {

/***/ "(ssr)/./node_modules/groq-sdk/_shims/MultipartBody.mjs":
/*!********************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/MultipartBody.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultipartBody: () => (/* binding */ MultipartBody)\n/* harmony export */ });\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nclass MultipartBody {\n    constructor(body) {\n        this.body = body;\n    }\n    get [Symbol.toStringTag]() {\n        return 'MultipartBody';\n    }\n}\n//# sourceMappingURL=MultipartBody.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvX3NoaW1zL011bHRpcGFydEJvZHkubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcV2lraVNlYXJjaFxcbm9kZV9tb2R1bGVzXFxncm9xLXNka1xcX3NoaW1zXFxNdWx0aXBhcnRCb2R5Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIERpc2NsYWltZXI6IG1vZHVsZXMgaW4gX3NoaW1zIGFyZW4ndCBpbnRlbmRlZCB0byBiZSBpbXBvcnRlZCBieSBTREsgdXNlcnMuXG4gKi9cbmV4cG9ydCBjbGFzcyBNdWx0aXBhcnRCb2R5IHtcbiAgICBjb25zdHJ1Y3Rvcihib2R5KSB7XG4gICAgICAgIHRoaXMuYm9keSA9IGJvZHk7XG4gICAgfVxuICAgIGdldCBbU3ltYm9sLnRvU3RyaW5nVGFnXSgpIHtcbiAgICAgICAgcmV0dXJuICdNdWx0aXBhcnRCb2R5JztcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1NdWx0aXBhcnRCb2R5Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/_shims/MultipartBody.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/_shims/index.mjs":
/*!************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/index.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Blob),\n/* harmony export */   File: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.File),\n/* harmony export */   FormData: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.FormData),\n/* harmony export */   Headers: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Headers),\n/* harmony export */   ReadableStream: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.ReadableStream),\n/* harmony export */   Request: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Request),\n/* harmony export */   Response: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Response),\n/* harmony export */   auto: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.auto),\n/* harmony export */   fetch: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.fetch),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFromPath),\n/* harmony export */   getDefaultAgent: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.getDefaultAgent),\n/* harmony export */   getMultipartRequestOptions: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions),\n/* harmony export */   init: () => (/* binding */ init),\n/* harmony export */   isFsReadStream: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.isFsReadStream),\n/* harmony export */   kind: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.kind),\n/* harmony export */   setShims: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.setShims)\n/* harmony export */ });\n/* harmony import */ var _registry_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry.mjs */ \"(ssr)/./node_modules/groq-sdk/_shims/registry.mjs\");\n/* harmony import */ var groq_sdk_shims_auto_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! groq-sdk/_shims/auto/runtime */ \"(ssr)/./node_modules/groq-sdk/_shims/node-runtime.mjs\");\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\n\n\nconst init = () => {\n  if (!_registry_mjs__WEBPACK_IMPORTED_MODULE_0__.kind) _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.setShims(groq_sdk_shims_auto_runtime__WEBPACK_IMPORTED_MODULE_1__.getRuntime(), { auto: true });\n};\n\n\ninit();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvX3NoaW1zL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDd0M7QUFDYTtBQUM5QztBQUNQLE9BQU8sK0NBQVUsRUFBRSxtREFBYyxDQUFDLG1FQUFlLE1BQU0sWUFBWTtBQUNuRTtBQUMrQjs7QUFFL0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcV2lraVNlYXJjaFxcbm9kZV9tb2R1bGVzXFxncm9xLXNka1xcX3NoaW1zXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBEaXNjbGFpbWVyOiBtb2R1bGVzIGluIF9zaGltcyBhcmVuJ3QgaW50ZW5kZWQgdG8gYmUgaW1wb3J0ZWQgYnkgU0RLIHVzZXJzLlxuICovXG5pbXBvcnQgKiBhcyBzaGltcyBmcm9tICcuL3JlZ2lzdHJ5Lm1qcyc7XG5pbXBvcnQgKiBhcyBhdXRvIGZyb20gJ2dyb3Etc2RrL19zaGltcy9hdXRvL3J1bnRpbWUnO1xuZXhwb3J0IGNvbnN0IGluaXQgPSAoKSA9PiB7XG4gIGlmICghc2hpbXMua2luZCkgc2hpbXMuc2V0U2hpbXMoYXV0by5nZXRSdW50aW1lKCksIHsgYXV0bzogdHJ1ZSB9KTtcbn07XG5leHBvcnQgKiBmcm9tICcuL3JlZ2lzdHJ5Lm1qcyc7XG5cbmluaXQoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/_shims/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/_shims/node-runtime.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/node-runtime.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntime: () => (/* binding */ getRuntime)\n/* harmony export */ });\n/* harmony import */ var node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node-fetch */ \"(ssr)/./node_modules/node-fetch/lib/index.mjs\");\n/* harmony import */ var formdata_node__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! formdata-node */ \"(ssr)/./node_modules/formdata-node/lib/esm/index.js\");\n/* harmony import */ var agentkeepalive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! agentkeepalive */ \"(ssr)/./node_modules/agentkeepalive/index.js\");\n/* harmony import */ var abort_controller__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! abort-controller */ \"(ssr)/./node_modules/abort-controller/dist/abort-controller.js\");\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var form_data_encoder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! form-data-encoder */ \"(ssr)/./node_modules/form-data-encoder/lib/esm/index.js\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var _MultipartBody_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MultipartBody.mjs */ \"(ssr)/./node_modules/groq-sdk/_shims/MultipartBody.mjs\");\n/* harmony import */ var node_stream_web__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! node:stream/web */ \"node:stream/web\");\n\n\n\n\n\n\n\n\n\nlet fileFromPathWarned = false;\nasync function fileFromPath(path, ...args) {\n    // this import fails in environments that don't handle export maps correctly, like old versions of Jest\n    const { fileFromPath: _fileFromPath } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/formdata-node\"), __webpack_require__.e(\"vendor-chunks/node-domexception\")]).then(__webpack_require__.bind(__webpack_require__, /*! formdata-node/file-from-path */ \"(ssr)/./node_modules/formdata-node/lib/esm/fileFromPath.js\"));\n    if (!fileFromPathWarned) {\n        console.warn(`fileFromPath is deprecated; use fs.createReadStream(${JSON.stringify(path)}) instead`);\n        fileFromPathWarned = true;\n    }\n    // @ts-ignore\n    return await _fileFromPath(path, ...args);\n}\nconst defaultHttpAgent = new agentkeepalive__WEBPACK_IMPORTED_MODULE_2__({ keepAlive: true, timeout: 5 * 60 * 1000 });\nconst defaultHttpsAgent = new agentkeepalive__WEBPACK_IMPORTED_MODULE_2__.HttpsAgent({ keepAlive: true, timeout: 5 * 60 * 1000 });\nasync function getMultipartRequestOptions(form, opts) {\n    const encoder = new form_data_encoder__WEBPACK_IMPORTED_MODULE_5__.FormDataEncoder(form);\n    const readable = node_stream__WEBPACK_IMPORTED_MODULE_6__.Readable.from(encoder);\n    const body = new _MultipartBody_mjs__WEBPACK_IMPORTED_MODULE_8__.MultipartBody(readable);\n    const headers = {\n        ...opts.headers,\n        ...encoder.headers,\n        'Content-Length': encoder.contentLength,\n    };\n    return { ...opts, body: body, headers };\n}\nfunction getRuntime() {\n    // Polyfill global object if needed.\n    if (typeof AbortController === 'undefined') {\n        // @ts-expect-error (the types are subtly different, but compatible in practice)\n        globalThis.AbortController = abort_controller__WEBPACK_IMPORTED_MODULE_3__.AbortController;\n    }\n    return {\n        kind: 'node',\n        fetch: node_fetch__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        Request: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Request,\n        Response: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Response,\n        Headers: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers,\n        FormData: formdata_node__WEBPACK_IMPORTED_MODULE_1__.FormData,\n        Blob: formdata_node__WEBPACK_IMPORTED_MODULE_1__.Blob,\n        File: formdata_node__WEBPACK_IMPORTED_MODULE_1__.File,\n        ReadableStream: node_stream_web__WEBPACK_IMPORTED_MODULE_7__.ReadableStream,\n        getMultipartRequestOptions,\n        getDefaultAgent: (url) => (url.startsWith('https') ? defaultHttpsAgent : defaultHttpAgent),\n        fileFromPath,\n        isFsReadStream: (value) => value instanceof node_fs__WEBPACK_IMPORTED_MODULE_4__.ReadStream,\n    };\n}\n//# sourceMappingURL=node-runtime.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/_shims/node-runtime.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/_shims/registry.mjs":
/*!***************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/registry.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   File: () => (/* binding */ File),\n/* harmony export */   FormData: () => (/* binding */ FormData),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   ReadableStream: () => (/* binding */ ReadableStream),\n/* harmony export */   Request: () => (/* binding */ Request),\n/* harmony export */   Response: () => (/* binding */ Response),\n/* harmony export */   auto: () => (/* binding */ auto),\n/* harmony export */   fetch: () => (/* binding */ fetch),\n/* harmony export */   fileFromPath: () => (/* binding */ fileFromPath),\n/* harmony export */   getDefaultAgent: () => (/* binding */ getDefaultAgent),\n/* harmony export */   getMultipartRequestOptions: () => (/* binding */ getMultipartRequestOptions),\n/* harmony export */   isFsReadStream: () => (/* binding */ isFsReadStream),\n/* harmony export */   kind: () => (/* binding */ kind),\n/* harmony export */   setShims: () => (/* binding */ setShims)\n/* harmony export */ });\nlet auto = false;\nlet kind = undefined;\nlet fetch = undefined;\nlet Request = undefined;\nlet Response = undefined;\nlet Headers = undefined;\nlet FormData = undefined;\nlet Blob = undefined;\nlet File = undefined;\nlet ReadableStream = undefined;\nlet getMultipartRequestOptions = undefined;\nlet getDefaultAgent = undefined;\nlet fileFromPath = undefined;\nlet isFsReadStream = undefined;\nfunction setShims(shims, options = { auto: false }) {\n    if (auto) {\n        throw new Error(`you must \\`import 'groq-sdk/shims/${shims.kind}'\\` before importing anything else from groq-sdk`);\n    }\n    if (kind) {\n        throw new Error(`can't \\`import 'groq-sdk/shims/${shims.kind}'\\` after \\`import 'groq-sdk/shims/${kind}'\\``);\n    }\n    auto = options.auto;\n    kind = shims.kind;\n    fetch = shims.fetch;\n    Request = shims.Request;\n    Response = shims.Response;\n    Headers = shims.Headers;\n    FormData = shims.FormData;\n    Blob = shims.Blob;\n    File = shims.File;\n    ReadableStream = shims.ReadableStream;\n    getMultipartRequestOptions = shims.getMultipartRequestOptions;\n    getDefaultAgent = shims.getDefaultAgent;\n    fileFromPath = shims.fileFromPath;\n    isFsReadStream = shims.isFsReadStream;\n}\n//# sourceMappingURL=registry.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/_shims/registry.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/core.mjs":
/*!****************************************!*\
  !*** ./node_modules/groq-sdk/core.mjs ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIClient: () => (/* binding */ APIClient),\n/* harmony export */   APIPromise: () => (/* binding */ APIPromise),\n/* harmony export */   AbstractPage: () => (/* binding */ AbstractPage),\n/* harmony export */   PagePromise: () => (/* binding */ PagePromise),\n/* harmony export */   castToError: () => (/* binding */ castToError),\n/* harmony export */   coerceBoolean: () => (/* binding */ coerceBoolean),\n/* harmony export */   coerceFloat: () => (/* binding */ coerceFloat),\n/* harmony export */   coerceInteger: () => (/* binding */ coerceInteger),\n/* harmony export */   createForm: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.createForm),\n/* harmony export */   createResponseHeaders: () => (/* binding */ createResponseHeaders),\n/* harmony export */   debug: () => (/* binding */ debug),\n/* harmony export */   ensurePresent: () => (/* binding */ ensurePresent),\n/* harmony export */   getHeader: () => (/* binding */ getHeader),\n/* harmony export */   getRequiredHeader: () => (/* binding */ getRequiredHeader),\n/* harmony export */   hasOwn: () => (/* binding */ hasOwn),\n/* harmony export */   isEmptyObj: () => (/* binding */ isEmptyObj),\n/* harmony export */   isHeadersProtocol: () => (/* binding */ isHeadersProtocol),\n/* harmony export */   isObj: () => (/* binding */ isObj),\n/* harmony export */   isRequestOptions: () => (/* binding */ isRequestOptions),\n/* harmony export */   isRunningInBrowser: () => (/* binding */ isRunningInBrowser),\n/* harmony export */   maybeCoerceBoolean: () => (/* binding */ maybeCoerceBoolean),\n/* harmony export */   maybeCoerceFloat: () => (/* binding */ maybeCoerceFloat),\n/* harmony export */   maybeCoerceInteger: () => (/* binding */ maybeCoerceInteger),\n/* harmony export */   maybeMultipartFormRequestOptions: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.maybeMultipartFormRequestOptions),\n/* harmony export */   multipartFormRequestOptions: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions),\n/* harmony export */   readEnv: () => (/* binding */ readEnv),\n/* harmony export */   safeJSON: () => (/* binding */ safeJSON),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   toBase64: () => (/* binding */ toBase64)\n/* harmony export */ });\n/* harmony import */ var _version_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./version.mjs */ \"(ssr)/./node_modules/groq-sdk/version.mjs\");\n/* harmony import */ var _lib_streaming_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/streaming.mjs */ \"(ssr)/./node_modules/groq-sdk/lib/streaming.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./error.mjs */ \"(ssr)/./node_modules/groq-sdk/error.mjs\");\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_shims/index.mjs */ \"(ssr)/./node_modules/groq-sdk/_shims/index.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./uploads.mjs */ \"(ssr)/./node_modules/groq-sdk/uploads.mjs\");\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _AbstractPage_client;\n\n\n\n\n// try running side effects outside of _shims/index to workaround https://github.com/vercel/next.js/issues/76881\n(0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.init)();\n\n\nasync function defaultParseResponse(props) {\n    const { response } = props;\n    if (props.options.stream) {\n        debug('response', response.status, response.url, response.headers, response.body);\n        // Note: there is an invariant here that isn't represented in the type system\n        // that if you set `stream: true` the response type must also be `Stream<T>`\n        if (props.options.__streamClass) {\n            return props.options.__streamClass.fromSSEResponse(response, props.controller);\n        }\n        return _lib_streaming_mjs__WEBPACK_IMPORTED_MODULE_2__.Stream.fromSSEResponse(response, props.controller);\n    }\n    // fetch refuses to read the body when the status code is 204.\n    if (response.status === 204) {\n        return null;\n    }\n    if (props.options.__binaryResponse) {\n        return response;\n    }\n    const contentType = response.headers.get('content-type');\n    const mediaType = contentType?.split(';')[0]?.trim();\n    const isJSON = mediaType?.includes('application/json') || mediaType?.endsWith('+json');\n    if (isJSON) {\n        const json = await response.json();\n        debug('response', response.status, response.url, response.headers, json);\n        return json;\n    }\n    const text = await response.text();\n    debug('response', response.status, response.url, response.headers, text);\n    // TODO handle blob, arraybuffer, other content types, etc.\n    return text;\n}\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */\nclass APIPromise extends Promise {\n    constructor(responsePromise, parseResponse = defaultParseResponse) {\n        super((resolve) => {\n            // this is maybe a bit weird but this has to be a no-op to not implicitly\n            // parse the response body; instead .then, .catch, .finally are overridden\n            // to parse the response\n            resolve(null);\n        });\n        this.responsePromise = responsePromise;\n        this.parseResponse = parseResponse;\n    }\n    _thenUnwrap(transform) {\n        return new APIPromise(this.responsePromise, async (props) => transform(await this.parseResponse(props), props));\n    }\n    /**\n     * Gets the raw `Response` instance instead of parsing the response\n     * data.\n     *\n     * If you want to parse the response body but still get the `Response`\n     * instance, you can use {@link withResponse()}.\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from 'groq-sdk'`:\n     * - `import 'groq-sdk/shims/node'` (if you're running on Node)\n     * - `import 'groq-sdk/shims/web'` (otherwise)\n     */\n    asResponse() {\n        return this.responsePromise.then((p) => p.response);\n    }\n    /**\n     * Gets the parsed response data and the raw `Response` instance.\n     *\n     * If you just want to get the raw `Response` instance without parsing it,\n     * you can use {@link asResponse()}.\n     *\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from 'groq-sdk'`:\n     * - `import 'groq-sdk/shims/node'` (if you're running on Node)\n     * - `import 'groq-sdk/shims/web'` (otherwise)\n     */\n    async withResponse() {\n        const [data, response] = await Promise.all([this.parse(), this.asResponse()]);\n        return { data, response };\n    }\n    parse() {\n        if (!this.parsedPromise) {\n            this.parsedPromise = this.responsePromise.then(this.parseResponse);\n        }\n        return this.parsedPromise;\n    }\n    then(onfulfilled, onrejected) {\n        return this.parse().then(onfulfilled, onrejected);\n    }\n    catch(onrejected) {\n        return this.parse().catch(onrejected);\n    }\n    finally(onfinally) {\n        return this.parse().finally(onfinally);\n    }\n}\nclass APIClient {\n    constructor({ baseURL, maxRetries = 2, timeout = 60000, // 1 minute\n    httpAgent, fetch: overriddenFetch, }) {\n        this.baseURL = baseURL;\n        this.maxRetries = validatePositiveInteger('maxRetries', maxRetries);\n        this.timeout = validatePositiveInteger('timeout', timeout);\n        this.httpAgent = httpAgent;\n        this.fetch = overriddenFetch ?? _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.fetch;\n    }\n    authHeaders(opts) {\n        return {};\n    }\n    /**\n     * Override this to add your own default headers, for example:\n     *\n     *  {\n     *    ...super.defaultHeaders(),\n     *    Authorization: 'Bearer 123',\n     *  }\n     */\n    defaultHeaders(opts) {\n        return {\n            Accept: 'application/json',\n            'Content-Type': 'application/json',\n            'User-Agent': this.getUserAgent(),\n            ...getPlatformHeaders(),\n            ...this.authHeaders(opts),\n        };\n    }\n    /**\n     * Override this to add your own headers validation:\n     */\n    validateHeaders(headers, customHeaders) { }\n    defaultIdempotencyKey() {\n        return `stainless-node-retry-${uuid4()}`;\n    }\n    get(path, opts) {\n        return this.methodRequest('get', path, opts);\n    }\n    post(path, opts) {\n        return this.methodRequest('post', path, opts);\n    }\n    patch(path, opts) {\n        return this.methodRequest('patch', path, opts);\n    }\n    put(path, opts) {\n        return this.methodRequest('put', path, opts);\n    }\n    delete(path, opts) {\n        return this.methodRequest('delete', path, opts);\n    }\n    methodRequest(method, path, opts) {\n        return this.request(Promise.resolve(opts).then(async (opts) => {\n            const body = opts && (0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isBlobLike)(opts?.body) ? new DataView(await opts.body.arrayBuffer())\n                : opts?.body instanceof DataView ? opts.body\n                    : opts?.body instanceof ArrayBuffer ? new DataView(opts.body)\n                        : opts && ArrayBuffer.isView(opts?.body) ? new DataView(opts.body.buffer)\n                            : opts?.body;\n            return { method, path, ...opts, body };\n        }));\n    }\n    getAPIList(path, Page, opts) {\n        return this.requestAPIList(Page, { method: 'get', path, ...opts });\n    }\n    calculateContentLength(body) {\n        if (typeof body === 'string') {\n            if (typeof Buffer !== 'undefined') {\n                return Buffer.byteLength(body, 'utf8').toString();\n            }\n            if (typeof TextEncoder !== 'undefined') {\n                const encoder = new TextEncoder();\n                const encoded = encoder.encode(body);\n                return encoded.length.toString();\n            }\n        }\n        else if (ArrayBuffer.isView(body)) {\n            return body.byteLength.toString();\n        }\n        return null;\n    }\n    buildRequest(inputOptions, { retryCount = 0 } = {}) {\n        const options = { ...inputOptions };\n        const { method, path, query, headers: headers = {} } = options;\n        const body = ArrayBuffer.isView(options.body) || (options.__binaryRequest && typeof options.body === 'string') ?\n            options.body\n            : (0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isMultipartBody)(options.body) ? options.body.body\n                : options.body ? JSON.stringify(options.body, null, 2)\n                    : null;\n        const contentLength = this.calculateContentLength(body);\n        const url = this.buildURL(path, query);\n        if ('timeout' in options)\n            validatePositiveInteger('timeout', options.timeout);\n        options.timeout = options.timeout ?? this.timeout;\n        const httpAgent = options.httpAgent ?? this.httpAgent ?? (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getDefaultAgent)(url);\n        const minAgentTimeout = options.timeout + 1000;\n        if (typeof httpAgent?.options?.timeout === 'number' &&\n            minAgentTimeout > (httpAgent.options.timeout ?? 0)) {\n            // Allow any given request to bump our agent active socket timeout.\n            // This may seem strange, but leaking active sockets should be rare and not particularly problematic,\n            // and without mutating agent we would need to create more of them.\n            // This tradeoff optimizes for performance.\n            httpAgent.options.timeout = minAgentTimeout;\n        }\n        if (this.idempotencyHeader && method !== 'get') {\n            if (!inputOptions.idempotencyKey)\n                inputOptions.idempotencyKey = this.defaultIdempotencyKey();\n            headers[this.idempotencyHeader] = inputOptions.idempotencyKey;\n        }\n        const reqHeaders = this.buildHeaders({ options, headers, contentLength, retryCount });\n        const req = {\n            method,\n            ...(body && { body: body }),\n            headers: reqHeaders,\n            ...(httpAgent && { agent: httpAgent }),\n            // @ts-ignore node-fetch uses a custom AbortSignal type that is\n            // not compatible with standard web types\n            signal: options.signal ?? null,\n        };\n        return { req, url, timeout: options.timeout };\n    }\n    buildHeaders({ options, headers, contentLength, retryCount, }) {\n        const reqHeaders = {};\n        if (contentLength) {\n            reqHeaders['content-length'] = contentLength;\n        }\n        const defaultHeaders = this.defaultHeaders(options);\n        applyHeadersMut(reqHeaders, defaultHeaders);\n        applyHeadersMut(reqHeaders, headers);\n        // let builtin fetch set the Content-Type for multipart bodies\n        if ((0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isMultipartBody)(options.body) && _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.kind !== 'node') {\n            delete reqHeaders['content-type'];\n        }\n        // Don't set theses headers if they were already set or removed through default headers or by the caller.\n        // We check `defaultHeaders` and `headers`, which can contain nulls, instead of `reqHeaders` to account\n        // for the removal case.\n        if (getHeader(defaultHeaders, 'x-stainless-retry-count') === undefined &&\n            getHeader(headers, 'x-stainless-retry-count') === undefined) {\n            reqHeaders['x-stainless-retry-count'] = String(retryCount);\n        }\n        if (getHeader(defaultHeaders, 'x-stainless-timeout') === undefined &&\n            getHeader(headers, 'x-stainless-timeout') === undefined &&\n            options.timeout) {\n            reqHeaders['x-stainless-timeout'] = String(Math.trunc(options.timeout / 1000));\n        }\n        this.validateHeaders(reqHeaders, headers);\n        return reqHeaders;\n    }\n    /**\n     * Used as a callback for mutating the given `FinalRequestOptions` object.\n     */\n    async prepareOptions(options) { }\n    /**\n     * Used as a callback for mutating the given `RequestInit` object.\n     *\n     * This is useful for cases where you want to add certain headers based off of\n     * the request properties, e.g. `method` or `url`.\n     */\n    async prepareRequest(request, { url, options }) { }\n    parseHeaders(headers) {\n        return (!headers ? {}\n            : Symbol.iterator in headers ?\n                Object.fromEntries(Array.from(headers).map((header) => [...header]))\n                : { ...headers });\n    }\n    makeStatusError(status, error, message, headers) {\n        return _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIError.generate(status, error, message, headers);\n    }\n    request(options, remainingRetries = null) {\n        return new APIPromise(this.makeRequest(options, remainingRetries));\n    }\n    async makeRequest(optionsInput, retriesRemaining) {\n        const options = await optionsInput;\n        const maxRetries = options.maxRetries ?? this.maxRetries;\n        if (retriesRemaining == null) {\n            retriesRemaining = maxRetries;\n        }\n        await this.prepareOptions(options);\n        const { req, url, timeout } = this.buildRequest(options, { retryCount: maxRetries - retriesRemaining });\n        await this.prepareRequest(req, { url, options });\n        debug('request', url, options, req.headers);\n        if (options.signal?.aborted) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIUserAbortError();\n        }\n        const controller = new AbortController();\n        const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(castToError);\n        if (response instanceof Error) {\n            if (options.signal?.aborted) {\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIUserAbortError();\n            }\n            if (retriesRemaining) {\n                return this.retryRequest(options, retriesRemaining);\n            }\n            if (response.name === 'AbortError') {\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIConnectionTimeoutError();\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIConnectionError({ cause: response });\n        }\n        const responseHeaders = createResponseHeaders(response.headers);\n        if (!response.ok) {\n            if (retriesRemaining && this.shouldRetry(response)) {\n                const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n                debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders);\n                return this.retryRequest(options, retriesRemaining, responseHeaders);\n            }\n            const errText = await response.text().catch((e) => castToError(e).message);\n            const errJSON = safeJSON(errText);\n            const errMessage = errJSON ? undefined : errText;\n            const retryMessage = retriesRemaining ? `(error; no more retries left)` : `(error; not retryable)`;\n            debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders, errMessage);\n            const err = this.makeStatusError(response.status, errJSON, errMessage, responseHeaders);\n            throw err;\n        }\n        return { response, options, controller };\n    }\n    requestAPIList(Page, options) {\n        const request = this.makeRequest(options, null);\n        return new PagePromise(this, request, Page);\n    }\n    buildURL(path, query) {\n        const url = isAbsoluteURL(path) ?\n            new URL(path)\n            : new URL(this.baseURL + (this.baseURL.endsWith('/') && path.startsWith('/') ? path.slice(1) : path));\n        const defaultQuery = this.defaultQuery();\n        if (!isEmptyObj(defaultQuery)) {\n            query = { ...defaultQuery, ...query };\n        }\n        if (typeof query === 'object' && query && !Array.isArray(query)) {\n            url.search = this.stringifyQuery(query);\n        }\n        return url.toString();\n    }\n    stringifyQuery(query) {\n        return Object.entries(query)\n            .filter(([_, value]) => typeof value !== 'undefined')\n            .map(([key, value]) => {\n            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n            }\n            if (value === null) {\n                return `${encodeURIComponent(key)}=`;\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`);\n        })\n            .join('&');\n    }\n    async fetchWithTimeout(url, init, ms, controller) {\n        const { signal, ...options } = init || {};\n        if (signal)\n            signal.addEventListener('abort', () => controller.abort());\n        const timeout = setTimeout(() => controller.abort(), ms);\n        const fetchOptions = {\n            signal: controller.signal,\n            ...options,\n        };\n        if (fetchOptions.method) {\n            // Custom methods like 'patch' need to be uppercased\n            // See https://github.com/nodejs/undici/issues/2294\n            fetchOptions.method = fetchOptions.method.toUpperCase();\n        }\n        return (\n        // use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n        this.fetch.call(undefined, url, fetchOptions).finally(() => {\n            clearTimeout(timeout);\n        }));\n    }\n    shouldRetry(response) {\n        // Note this is not a standard header.\n        const shouldRetryHeader = response.headers.get('x-should-retry');\n        // If the server explicitly says whether or not to retry, obey.\n        if (shouldRetryHeader === 'true')\n            return true;\n        if (shouldRetryHeader === 'false')\n            return false;\n        // Retry on request timeouts.\n        if (response.status === 408)\n            return true;\n        // Retry on lock timeouts.\n        if (response.status === 409)\n            return true;\n        // Retry on rate limits.\n        if (response.status === 429)\n            return true;\n        // Retry internal errors.\n        if (response.status >= 500)\n            return true;\n        return false;\n    }\n    async retryRequest(options, retriesRemaining, responseHeaders) {\n        let timeoutMillis;\n        // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n        const retryAfterMillisHeader = responseHeaders?.['retry-after-ms'];\n        if (retryAfterMillisHeader) {\n            const timeoutMs = parseFloat(retryAfterMillisHeader);\n            if (!Number.isNaN(timeoutMs)) {\n                timeoutMillis = timeoutMs;\n            }\n        }\n        // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n        const retryAfterHeader = responseHeaders?.['retry-after'];\n        if (retryAfterHeader && !timeoutMillis) {\n            const timeoutSeconds = parseFloat(retryAfterHeader);\n            if (!Number.isNaN(timeoutSeconds)) {\n                timeoutMillis = timeoutSeconds * 1000;\n            }\n            else {\n                timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n            }\n        }\n        // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n        // just do what it says, but otherwise calculate a default\n        if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n            const maxRetries = options.maxRetries ?? this.maxRetries;\n            timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n        }\n        await sleep(timeoutMillis);\n        return this.makeRequest(options, retriesRemaining - 1);\n    }\n    calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries) {\n        const initialRetryDelay = 0.5;\n        const maxRetryDelay = 8.0;\n        const numRetries = maxRetries - retriesRemaining;\n        // Apply exponential backoff, but not more than the max.\n        const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n        // Apply some jitter, take up to at most 25 percent of the retry time.\n        const jitter = 1 - Math.random() * 0.25;\n        return sleepSeconds * jitter * 1000;\n    }\n    getUserAgent() {\n        return `${this.constructor.name}/JS ${_version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION}`;\n    }\n}\nclass AbstractPage {\n    constructor(client, response, body, options) {\n        _AbstractPage_client.set(this, void 0);\n        __classPrivateFieldSet(this, _AbstractPage_client, client, \"f\");\n        this.options = options;\n        this.response = response;\n        this.body = body;\n    }\n    hasNextPage() {\n        const items = this.getPaginatedItems();\n        if (!items.length)\n            return false;\n        return this.nextPageInfo() != null;\n    }\n    async getNextPage() {\n        const nextInfo = this.nextPageInfo();\n        if (!nextInfo) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError('No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.');\n        }\n        const nextOptions = { ...this.options };\n        if ('params' in nextInfo && typeof nextOptions.query === 'object') {\n            nextOptions.query = { ...nextOptions.query, ...nextInfo.params };\n        }\n        else if ('url' in nextInfo) {\n            const params = [...Object.entries(nextOptions.query || {}), ...nextInfo.url.searchParams.entries()];\n            for (const [key, value] of params) {\n                nextInfo.url.searchParams.set(key, value);\n            }\n            nextOptions.query = undefined;\n            nextOptions.path = nextInfo.url.toString();\n        }\n        return await __classPrivateFieldGet(this, _AbstractPage_client, \"f\").requestAPIList(this.constructor, nextOptions);\n    }\n    async *iterPages() {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        let page = this;\n        yield page;\n        while (page.hasNextPage()) {\n            page = await page.getNextPage();\n            yield page;\n        }\n    }\n    async *[(_AbstractPage_client = new WeakMap(), Symbol.asyncIterator)]() {\n        for await (const page of this.iterPages()) {\n            for (const item of page.getPaginatedItems()) {\n                yield item;\n            }\n        }\n    }\n}\n/**\n * This subclass of Promise will resolve to an instantiated Page once the request completes.\n *\n * It also implements AsyncIterable to allow auto-paginating iteration on an unawaited list call, eg:\n *\n *    for await (const item of client.items.list()) {\n *      console.log(item)\n *    }\n */\nclass PagePromise extends APIPromise {\n    constructor(client, request, Page) {\n        super(request, async (props) => new Page(client, props.response, await defaultParseResponse(props), props.options));\n    }\n    /**\n     * Allow auto-paginating iteration on an unawaited list call, eg:\n     *\n     *    for await (const item of client.items.list()) {\n     *      console.log(item)\n     *    }\n     */\n    async *[Symbol.asyncIterator]() {\n        const page = await this;\n        for await (const item of page) {\n            yield item;\n        }\n    }\n}\nconst createResponseHeaders = (headers) => {\n    return new Proxy(Object.fromEntries(\n    // @ts-ignore\n    headers.entries()), {\n        get(target, name) {\n            const key = name.toString();\n            return target[key.toLowerCase()] || target[key];\n        },\n    });\n};\n// This is required so that we can determine if a given object matches the RequestOptions\n// type at runtime. While this requires duplication, it is enforced by the TypeScript\n// compiler such that any missing / extraneous keys will cause an error.\nconst requestOptionsKeys = {\n    method: true,\n    path: true,\n    query: true,\n    body: true,\n    headers: true,\n    maxRetries: true,\n    stream: true,\n    timeout: true,\n    httpAgent: true,\n    signal: true,\n    idempotencyKey: true,\n    __binaryRequest: true,\n    __binaryResponse: true,\n    __streamClass: true,\n};\nconst isRequestOptions = (obj) => {\n    return (typeof obj === 'object' &&\n        obj !== null &&\n        !isEmptyObj(obj) &&\n        Object.keys(obj).every((k) => hasOwn(requestOptionsKeys, k)));\n};\nconst getPlatformProperties = () => {\n    if (typeof Deno !== 'undefined' && Deno.build != null) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            'X-Stainless-OS': normalizePlatform(Deno.build.os),\n            'X-Stainless-Arch': normalizeArch(Deno.build.arch),\n            'X-Stainless-Runtime': 'deno',\n            'X-Stainless-Runtime-Version': typeof Deno.version === 'string' ? Deno.version : Deno.version?.deno ?? 'unknown',\n        };\n    }\n    if (typeof EdgeRuntime !== 'undefined') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': `other:${EdgeRuntime}`,\n            'X-Stainless-Runtime': 'edge',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    // Check if Node.js\n    if (Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            'X-Stainless-OS': normalizePlatform(process.platform),\n            'X-Stainless-Arch': normalizeArch(process.arch),\n            'X-Stainless-Runtime': 'node',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    const browserInfo = getBrowserInfo();\n    if (browserInfo) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': 'unknown',\n            'X-Stainless-Runtime': `browser:${browserInfo.browser}`,\n            'X-Stainless-Runtime-Version': browserInfo.version,\n        };\n    }\n    // TODO add support for Cloudflare workers, etc.\n    return {\n        'X-Stainless-Lang': 'js',\n        'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n        'X-Stainless-OS': 'Unknown',\n        'X-Stainless-Arch': 'unknown',\n        'X-Stainless-Runtime': 'unknown',\n        'X-Stainless-Runtime-Version': 'unknown',\n    };\n};\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo() {\n    if (typeof navigator === 'undefined' || !navigator) {\n        return null;\n    }\n    // NOTE: The order matters here!\n    const browserPatterns = [\n        { key: 'edge', pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'chrome', pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'firefox', pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'safari', pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/ },\n    ];\n    // Find the FIRST matching browser\n    for (const { key, pattern } of browserPatterns) {\n        const match = pattern.exec(navigator.userAgent);\n        if (match) {\n            const major = match[1] || 0;\n            const minor = match[2] || 0;\n            const patch = match[3] || 0;\n            return { browser: key, version: `${major}.${minor}.${patch}` };\n        }\n    }\n    return null;\n}\nconst normalizeArch = (arch) => {\n    // Node docs:\n    // - https://nodejs.org/api/process.html#processarch\n    // Deno docs:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    if (arch === 'x32')\n        return 'x32';\n    if (arch === 'x86_64' || arch === 'x64')\n        return 'x64';\n    if (arch === 'arm')\n        return 'arm';\n    if (arch === 'aarch64' || arch === 'arm64')\n        return 'arm64';\n    if (arch)\n        return `other:${arch}`;\n    return 'unknown';\n};\nconst normalizePlatform = (platform) => {\n    // Node platforms:\n    // - https://nodejs.org/api/process.html#processplatform\n    // Deno platforms:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    // - https://github.com/denoland/deno/issues/14799\n    platform = platform.toLowerCase();\n    // NOTE: this iOS check is untested and may not work\n    // Node does not work natively on IOS, there is a fork at\n    // https://github.com/nodejs-mobile/nodejs-mobile\n    // however it is unknown at the time of writing how to detect if it is running\n    if (platform.includes('ios'))\n        return 'iOS';\n    if (platform === 'android')\n        return 'Android';\n    if (platform === 'darwin')\n        return 'MacOS';\n    if (platform === 'win32')\n        return 'Windows';\n    if (platform === 'freebsd')\n        return 'FreeBSD';\n    if (platform === 'openbsd')\n        return 'OpenBSD';\n    if (platform === 'linux')\n        return 'Linux';\n    if (platform)\n        return `Other:${platform}`;\n    return 'Unknown';\n};\nlet _platformHeaders;\nconst getPlatformHeaders = () => {\n    return (_platformHeaders ?? (_platformHeaders = getPlatformProperties()));\n};\nconst safeJSON = (text) => {\n    try {\n        return JSON.parse(text);\n    }\n    catch (err) {\n        return undefined;\n    }\n};\n// https://url.spec.whatwg.org/#url-scheme-string\nconst startsWithSchemeRegexp = /^[a-z][a-z0-9+.-]*:/i;\nconst isAbsoluteURL = (url) => {\n    return startsWithSchemeRegexp.test(url);\n};\nconst sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\nconst validatePositiveInteger = (name, n) => {\n    if (typeof n !== 'number' || !Number.isInteger(n)) {\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`${name} must be an integer`);\n    }\n    if (n < 0) {\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`${name} must be a positive integer`);\n    }\n    return n;\n};\nconst castToError = (err) => {\n    if (err instanceof Error)\n        return err;\n    if (typeof err === 'object' && err !== null) {\n        try {\n            return new Error(JSON.stringify(err));\n        }\n        catch { }\n    }\n    return new Error(err);\n};\nconst ensurePresent = (value) => {\n    if (value == null)\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Expected a value to be given but received ${value} instead.`);\n    return value;\n};\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */\nconst readEnv = (env) => {\n    if (typeof process !== 'undefined') {\n        return process.env?.[env]?.trim() ?? undefined;\n    }\n    if (typeof Deno !== 'undefined') {\n        return Deno.env?.get?.(env)?.trim();\n    }\n    return undefined;\n};\nconst coerceInteger = (value) => {\n    if (typeof value === 'number')\n        return Math.round(value);\n    if (typeof value === 'string')\n        return parseInt(value, 10);\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nconst coerceFloat = (value) => {\n    if (typeof value === 'number')\n        return value;\n    if (typeof value === 'string')\n        return parseFloat(value);\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nconst coerceBoolean = (value) => {\n    if (typeof value === 'boolean')\n        return value;\n    if (typeof value === 'string')\n        return value === 'true';\n    return Boolean(value);\n};\nconst maybeCoerceInteger = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceInteger(value);\n};\nconst maybeCoerceFloat = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceFloat(value);\n};\nconst maybeCoerceBoolean = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceBoolean(value);\n};\n// https://stackoverflow.com/a/34491287\nfunction isEmptyObj(obj) {\n    if (!obj)\n        return true;\n    for (const _k in obj)\n        return false;\n    return true;\n}\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nfunction hasOwn(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n/**\n * Copies headers from \"newHeaders\" onto \"targetHeaders\",\n * using lower-case for all properties,\n * ignoring any keys with undefined values,\n * and deleting any keys with null values.\n */\nfunction applyHeadersMut(targetHeaders, newHeaders) {\n    for (const k in newHeaders) {\n        if (!hasOwn(newHeaders, k))\n            continue;\n        const lowerKey = k.toLowerCase();\n        if (!lowerKey)\n            continue;\n        const val = newHeaders[k];\n        if (val === null) {\n            delete targetHeaders[lowerKey];\n        }\n        else if (val !== undefined) {\n            targetHeaders[lowerKey] = val;\n        }\n    }\n}\nfunction debug(action, ...args) {\n    if (typeof process !== 'undefined' && process?.env?.['DEBUG'] === 'true') {\n        console.log(`Groq:DEBUG:${action}`, ...args);\n    }\n}\n/**\n * https://stackoverflow.com/a/2117523\n */\nconst uuid4 = () => {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16) | 0;\n        const v = c === 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n};\nconst isRunningInBrowser = () => {\n    return (\n    // @ts-ignore\n    typeof window !== 'undefined' &&\n        // @ts-ignore\n        typeof window.document !== 'undefined' &&\n        // @ts-ignore\n        typeof navigator !== 'undefined');\n};\nconst isHeadersProtocol = (headers) => {\n    return typeof headers?.get === 'function';\n};\nconst getRequiredHeader = (headers, header) => {\n    const foundHeader = getHeader(headers, header);\n    if (foundHeader === undefined) {\n        throw new Error(`Could not find ${header} header`);\n    }\n    return foundHeader;\n};\nconst getHeader = (headers, header) => {\n    const lowerCasedHeader = header.toLowerCase();\n    if (isHeadersProtocol(headers)) {\n        // to deal with the case where the header looks like Stainless-Event-Id\n        const intercapsHeader = header[0]?.toUpperCase() +\n            header.substring(1).replace(/([^\\w])(\\w)/g, (_m, g1, g2) => g1 + g2.toUpperCase());\n        for (const key of [header, lowerCasedHeader, header.toUpperCase(), intercapsHeader]) {\n            const value = headers.get(key);\n            if (value) {\n                return value;\n            }\n        }\n    }\n    for (const [key, value] of Object.entries(headers)) {\n        if (key.toLowerCase() === lowerCasedHeader) {\n            if (Array.isArray(value)) {\n                if (value.length <= 1)\n                    return value[0];\n                console.warn(`Received ${value.length} entries for the ${header} header, using the first entry.`);\n                return value[0];\n            }\n            return value;\n        }\n    }\n    return undefined;\n};\n/**\n * Encodes a string to Base64 format.\n */\nconst toBase64 = (str) => {\n    if (!str)\n        return '';\n    if (typeof Buffer !== 'undefined') {\n        return Buffer.from(str).toString('base64');\n    }\n    if (typeof btoa !== 'undefined') {\n        return btoa(str);\n    }\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError('Cannot generate b64 string; Expected `Buffer` or `btoa` to be defined');\n};\nfunction isObj(obj) {\n    return obj != null && typeof obj === 'object' && !Array.isArray(obj);\n}\n//# sourceMappingURL=core.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/core.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/error.mjs":
/*!*****************************************!*\
  !*** ./node_modules/groq-sdk/error.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIConnectionError: () => (/* binding */ APIConnectionError),\n/* harmony export */   APIConnectionTimeoutError: () => (/* binding */ APIConnectionTimeoutError),\n/* harmony export */   APIError: () => (/* binding */ APIError),\n/* harmony export */   APIUserAbortError: () => (/* binding */ APIUserAbortError),\n/* harmony export */   AuthenticationError: () => (/* binding */ AuthenticationError),\n/* harmony export */   BadRequestError: () => (/* binding */ BadRequestError),\n/* harmony export */   ConflictError: () => (/* binding */ ConflictError),\n/* harmony export */   GroqError: () => (/* binding */ GroqError),\n/* harmony export */   InternalServerError: () => (/* binding */ InternalServerError),\n/* harmony export */   NotFoundError: () => (/* binding */ NotFoundError),\n/* harmony export */   PermissionDeniedError: () => (/* binding */ PermissionDeniedError),\n/* harmony export */   RateLimitError: () => (/* binding */ RateLimitError),\n/* harmony export */   UnprocessableEntityError: () => (/* binding */ UnprocessableEntityError)\n/* harmony export */ });\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.mjs */ \"(ssr)/./node_modules/groq-sdk/core.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass GroqError extends Error {\n}\nclass APIError extends GroqError {\n    constructor(status, error, message, headers) {\n        super(`${APIError.makeMessage(status, error, message)}`);\n        this.status = status;\n        this.headers = headers;\n        this.error = error;\n    }\n    static makeMessage(status, error, message) {\n        const msg = error?.message ?\n            typeof error.message === 'string' ?\n                error.message\n                : JSON.stringify(error.message)\n            : error ? JSON.stringify(error)\n                : message;\n        if (status && msg) {\n            return `${status} ${msg}`;\n        }\n        if (status) {\n            return `${status} status code (no body)`;\n        }\n        if (msg) {\n            return msg;\n        }\n        return '(no status code or body)';\n    }\n    static generate(status, errorResponse, message, headers) {\n        if (!status || !headers) {\n            return new APIConnectionError({ message, cause: (0,_core_mjs__WEBPACK_IMPORTED_MODULE_0__.castToError)(errorResponse) });\n        }\n        const error = errorResponse;\n        if (status === 400) {\n            return new BadRequestError(status, error, message, headers);\n        }\n        if (status === 401) {\n            return new AuthenticationError(status, error, message, headers);\n        }\n        if (status === 403) {\n            return new PermissionDeniedError(status, error, message, headers);\n        }\n        if (status === 404) {\n            return new NotFoundError(status, error, message, headers);\n        }\n        if (status === 409) {\n            return new ConflictError(status, error, message, headers);\n        }\n        if (status === 422) {\n            return new UnprocessableEntityError(status, error, message, headers);\n        }\n        if (status === 429) {\n            return new RateLimitError(status, error, message, headers);\n        }\n        if (status >= 500) {\n            return new InternalServerError(status, error, message, headers);\n        }\n        return new APIError(status, error, message, headers);\n    }\n}\nclass APIUserAbortError extends APIError {\n    constructor({ message } = {}) {\n        super(undefined, undefined, message || 'Request was aborted.', undefined);\n    }\n}\nclass APIConnectionError extends APIError {\n    constructor({ message, cause }) {\n        super(undefined, undefined, message || 'Connection error.', undefined);\n        // in some environments the 'cause' property is already declared\n        // @ts-ignore\n        if (cause)\n            this.cause = cause;\n    }\n}\nclass APIConnectionTimeoutError extends APIConnectionError {\n    constructor({ message } = {}) {\n        super({ message: message ?? 'Request timed out.' });\n    }\n}\nclass BadRequestError extends APIError {\n}\nclass AuthenticationError extends APIError {\n}\nclass PermissionDeniedError extends APIError {\n}\nclass NotFoundError extends APIError {\n}\nclass ConflictError extends APIError {\n}\nclass UnprocessableEntityError extends APIError {\n}\nclass RateLimitError extends APIError {\n}\nclass InternalServerError extends APIError {\n}\n//# sourceMappingURL=error.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/error.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/index.mjs":
/*!*****************************************!*\
  !*** ./node_modules/groq-sdk/index.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIConnectionError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionError),\n/* harmony export */   APIConnectionTimeoutError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionTimeoutError),\n/* harmony export */   APIError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError),\n/* harmony export */   APIUserAbortError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIUserAbortError),\n/* harmony export */   AuthenticationError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.AuthenticationError),\n/* harmony export */   BadRequestError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.BadRequestError),\n/* harmony export */   ConflictError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.ConflictError),\n/* harmony export */   Groq: () => (/* binding */ Groq),\n/* harmony export */   GroqError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError),\n/* harmony export */   InternalServerError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.InternalServerError),\n/* harmony export */   NotFoundError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.NotFoundError),\n/* harmony export */   PermissionDeniedError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.PermissionDeniedError),\n/* harmony export */   RateLimitError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.RateLimitError),\n/* harmony export */   UnprocessableEntityError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.UnprocessableEntityError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_10__.fileFromPath),\n/* harmony export */   toFile: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_9__.toFile)\n/* harmony export */ });\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.mjs */ \"(ssr)/./node_modules/groq-sdk/core.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error.mjs */ \"(ssr)/./node_modules/groq-sdk/error.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./uploads.mjs */ \"(ssr)/./node_modules/groq-sdk/uploads.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./uploads.mjs */ \"(ssr)/./node_modules/groq-sdk/_shims/index.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resources/completions.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/completions.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resources/chat/chat.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/chat/chat.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./resources/embeddings.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/embeddings.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./resources/audio/audio.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/audio/audio.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./resources/models.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/models.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./resources/batches.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/batches.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./resources/files.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/files.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar _a;\n\n\n\n\n\n\n\n\n\n\n\n/**\n * API Client for interfacing with the Groq API.\n */\nclass Groq extends _core_mjs__WEBPACK_IMPORTED_MODULE_0__.APIClient {\n    /**\n     * API Client for interfacing with the Groq API.\n     *\n     * @param {string | undefined} [opts.apiKey=process.env['GROQ_API_KEY'] ?? undefined]\n     * @param {string} [opts.baseURL=process.env['GROQ_BASE_URL'] ?? https://api.groq.com] - Override the default base URL for the API.\n     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.\n     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.\n     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.\n     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.\n     * @param {boolean} [opts.dangerouslyAllowBrowser=false] - By default, client-side use of this library is not allowed, as it risks exposing your secret API credentials to attackers.\n     */\n    constructor({ baseURL = _core_mjs__WEBPACK_IMPORTED_MODULE_0__.readEnv('GROQ_BASE_URL'), apiKey = _core_mjs__WEBPACK_IMPORTED_MODULE_0__.readEnv('GROQ_API_KEY'), ...opts } = {}) {\n        if (apiKey === undefined) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(\"The GROQ_API_KEY environment variable is missing or empty; either provide it, or instantiate the Groq client with an apiKey option, like new Groq({ apiKey: 'My API Key' }).\");\n        }\n        const options = {\n            apiKey,\n            ...opts,\n            baseURL: baseURL || `https://api.groq.com`,\n        };\n        if (!options.dangerouslyAllowBrowser && _core_mjs__WEBPACK_IMPORTED_MODULE_0__.isRunningInBrowser()) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(\"It looks like you're running in a browser-like environment.\\n\\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\\nIf you understand the risks and have appropriate mitigations in place,\\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\\n\\nnew Groq({ apiKey, dangerouslyAllowBrowser: true })\");\n        }\n        super({\n            baseURL: options.baseURL,\n            timeout: options.timeout ?? 60000 /* 1 minute */,\n            httpAgent: options.httpAgent,\n            maxRetries: options.maxRetries,\n            fetch: options.fetch,\n        });\n        this.completions = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__.Completions(this);\n        this.chat = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_3__.Chat(this);\n        this.embeddings = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_4__.Embeddings(this);\n        this.audio = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_5__.Audio(this);\n        this.models = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_6__.Models(this);\n        this.batches = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_7__.Batches(this);\n        this.files = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_8__.Files(this);\n        this._options = options;\n        this.apiKey = apiKey;\n    }\n    defaultQuery() {\n        return this._options.defaultQuery;\n    }\n    defaultHeaders(opts) {\n        return {\n            ...super.defaultHeaders(opts),\n            ...this._options.defaultHeaders,\n        };\n    }\n    authHeaders(opts) {\n        return { Authorization: `Bearer ${this.apiKey}` };\n    }\n}\n_a = Groq;\nGroq.Groq = _a;\nGroq.DEFAULT_TIMEOUT = 60000; // 1 minute\nGroq.GroqError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError;\nGroq.APIError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError;\nGroq.APIConnectionError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionError;\nGroq.APIConnectionTimeoutError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionTimeoutError;\nGroq.APIUserAbortError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIUserAbortError;\nGroq.NotFoundError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.NotFoundError;\nGroq.ConflictError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.ConflictError;\nGroq.RateLimitError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.RateLimitError;\nGroq.BadRequestError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.BadRequestError;\nGroq.AuthenticationError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.AuthenticationError;\nGroq.InternalServerError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.InternalServerError;\nGroq.PermissionDeniedError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.PermissionDeniedError;\nGroq.UnprocessableEntityError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.UnprocessableEntityError;\nGroq.toFile = _uploads_mjs__WEBPACK_IMPORTED_MODULE_9__.toFile;\nGroq.fileFromPath = _uploads_mjs__WEBPACK_IMPORTED_MODULE_10__.fileFromPath;\nGroq.Completions = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__.Completions;\nGroq.Chat = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_3__.Chat;\nGroq.Embeddings = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_4__.Embeddings;\nGroq.Audio = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_5__.Audio;\nGroq.Models = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_6__.Models;\nGroq.Batches = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_7__.Batches;\nGroq.Files = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_8__.Files;\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Groq);\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/lib/streaming.mjs":
/*!*************************************************!*\
  !*** ./node_modules/groq-sdk/lib/streaming.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Stream: () => (/* binding */ Stream),\n/* harmony export */   readableStreamAsyncIterable: () => (/* binding */ readableStreamAsyncIterable)\n/* harmony export */ });\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_shims/index.mjs */ \"(ssr)/./node_modules/groq-sdk/_shims/index.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../error.mjs */ \"(ssr)/./node_modules/groq-sdk/error.mjs\");\n\n\n\nclass Stream {\n    constructor(iterator, controller) {\n        this.iterator = iterator;\n        this.controller = controller;\n    }\n    static fromSSEResponse(response, controller) {\n        let consumed = false;\n        const decoder = new SSEDecoder();\n        async function* iterMessages() {\n            if (!response.body) {\n                controller.abort();\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Attempted to iterate over a response with no body`);\n            }\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(response.body);\n            for await (const chunk of iter) {\n                for (const line of lineDecoder.decode(chunk)) {\n                    const sse = decoder.decode(line);\n                    if (sse)\n                        yield sse;\n                }\n            }\n            for (const line of lineDecoder.flush()) {\n                const sse = decoder.decode(line);\n                if (sse)\n                    yield sse;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const sse of iterMessages()) {\n                    if (done)\n                        continue;\n                    if (sse.data.startsWith('[DONE]')) {\n                        done = true;\n                        continue;\n                    }\n                    if (sse.event === null || sse.event === 'error') {\n                        let data;\n                        try {\n                            data = JSON.parse(sse.data);\n                        }\n                        catch (e) {\n                            console.error(`Could not parse message into JSON:`, sse.data);\n                            console.error(`From chunk:`, sse.raw);\n                            throw e;\n                        }\n                        if (data && data.error) {\n                            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError(data.error.status_code, data.error, data.error.message, undefined);\n                        }\n                        yield data;\n                    }\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    /**\n     * Generates a Stream from a newline-separated ReadableStream\n     * where each item is a JSON value.\n     */\n    static fromReadableStream(readableStream, controller) {\n        let consumed = false;\n        async function* iterLines() {\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(readableStream);\n            for await (const chunk of iter) {\n                for (const line of lineDecoder.decode(chunk)) {\n                    yield line;\n                }\n            }\n            for (const line of lineDecoder.flush()) {\n                yield line;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const line of iterLines()) {\n                    if (done)\n                        continue;\n                    if (line)\n                        yield JSON.parse(line);\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    [Symbol.asyncIterator]() {\n        return this.iterator();\n    }\n    /**\n     * Splits the stream into two streams which can be\n     * independently read from at different speeds.\n     */\n    tee() {\n        const left = [];\n        const right = [];\n        const iterator = this.iterator();\n        const teeIterator = (queue) => {\n            return {\n                next: () => {\n                    if (queue.length === 0) {\n                        const result = iterator.next();\n                        left.push(result);\n                        right.push(result);\n                    }\n                    return queue.shift();\n                },\n            };\n        };\n        return [\n            new Stream(() => teeIterator(left), this.controller),\n            new Stream(() => teeIterator(right), this.controller),\n        ];\n    }\n    /**\n     * Converts this stream to a newline-separated ReadableStream of\n     * JSON stringified values in the stream\n     * which can be turned back into a Stream with `Stream.fromReadableStream()`.\n     */\n    toReadableStream() {\n        const self = this;\n        let iter;\n        const encoder = new TextEncoder();\n        return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.ReadableStream({\n            async start() {\n                iter = self[Symbol.asyncIterator]();\n            },\n            async pull(ctrl) {\n                try {\n                    const { value, done } = await iter.next();\n                    if (done)\n                        return ctrl.close();\n                    const bytes = encoder.encode(JSON.stringify(value) + '\\n');\n                    ctrl.enqueue(bytes);\n                }\n                catch (err) {\n                    ctrl.error(err);\n                }\n            },\n            async cancel() {\n                await iter.return?.();\n            },\n        });\n    }\n}\nclass SSEDecoder {\n    constructor() {\n        this.event = null;\n        this.data = [];\n        this.chunks = [];\n    }\n    decode(line) {\n        if (line.endsWith('\\r')) {\n            line = line.substring(0, line.length - 1);\n        }\n        if (!line) {\n            // empty line and we didn't previously encounter any messages\n            if (!this.event && !this.data.length)\n                return null;\n            const sse = {\n                event: this.event,\n                data: this.data.join('\\n'),\n                raw: this.chunks,\n            };\n            this.event = null;\n            this.data = [];\n            this.chunks = [];\n            return sse;\n        }\n        this.chunks.push(line);\n        if (line.startsWith(':')) {\n            return null;\n        }\n        let [fieldname, _, value] = partition(line, ':');\n        if (value.startsWith(' ')) {\n            value = value.substring(1);\n        }\n        if (fieldname === 'event') {\n            this.event = value;\n        }\n        else if (fieldname === 'data') {\n            this.data.push(value);\n        }\n        return null;\n    }\n}\n/**\n * A re-implementation of httpx's `LineDecoder` in Python that handles incrementally\n * reading lines from text.\n *\n * https://github.com/encode/httpx/blob/920333ea98118e9cf617f246905d7b202510941c/httpx/_decoders.py#L258\n */\nclass LineDecoder {\n    constructor() {\n        this.buffer = [];\n        this.trailingCR = false;\n    }\n    decode(chunk) {\n        let text = this.decodeText(chunk);\n        if (this.trailingCR) {\n            text = '\\r' + text;\n            this.trailingCR = false;\n        }\n        if (text.endsWith('\\r')) {\n            this.trailingCR = true;\n            text = text.slice(0, -1);\n        }\n        if (!text) {\n            return [];\n        }\n        const trailingNewline = LineDecoder.NEWLINE_CHARS.has(text[text.length - 1] || '');\n        let lines = text.split(LineDecoder.NEWLINE_REGEXP);\n        if (lines.length === 1 && !trailingNewline) {\n            this.buffer.push(lines[0]);\n            return [];\n        }\n        if (this.buffer.length > 0) {\n            lines = [this.buffer.join('') + lines[0], ...lines.slice(1)];\n            this.buffer = [];\n        }\n        if (!trailingNewline) {\n            this.buffer = [lines.pop() || ''];\n        }\n        return lines;\n    }\n    decodeText(bytes) {\n        if (bytes == null)\n            return '';\n        if (typeof bytes === 'string')\n            return bytes;\n        // Node:\n        if (typeof Buffer !== 'undefined') {\n            if (bytes instanceof Buffer) {\n                return bytes.toString();\n            }\n            if (bytes instanceof Uint8Array) {\n                return Buffer.from(bytes).toString();\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Unexpected: received non-Uint8Array (${bytes.constructor.name}) stream chunk in an environment with a global \"Buffer\" defined, which this library assumes to be Node. Please report this error.`);\n        }\n        // Browser\n        if (typeof TextDecoder !== 'undefined') {\n            if (bytes instanceof Uint8Array || bytes instanceof ArrayBuffer) {\n                this.textDecoder ?? (this.textDecoder = new TextDecoder('utf8'));\n                return this.textDecoder.decode(bytes);\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Unexpected: received non-Uint8Array/ArrayBuffer (${bytes.constructor.name}) in a web platform. Please report this error.`);\n        }\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.`);\n    }\n    flush() {\n        if (!this.buffer.length && !this.trailingCR) {\n            return [];\n        }\n        const lines = [this.buffer.join('')];\n        this.buffer = [];\n        this.trailingCR = false;\n        return lines;\n    }\n}\n// prettier-ignore\nLineDecoder.NEWLINE_CHARS = new Set(['\\n', '\\r', '\\x0b', '\\x0c', '\\x1c', '\\x1d', '\\x1e', '\\x85', '\\u2028', '\\u2029']);\nLineDecoder.NEWLINE_REGEXP = /\\r\\n|[\\n\\r\\x0b\\x0c\\x1c\\x1d\\x1e\\x85\\u2028\\u2029]/g;\nfunction partition(str, delimiter) {\n    const index = str.indexOf(delimiter);\n    if (index !== -1) {\n        return [str.substring(0, index), delimiter, str.substring(index + delimiter.length)];\n    }\n    return [str, '', ''];\n}\n/**\n * Most browsers don't yet have async iterable support for ReadableStream,\n * and Node has a very different way of reading bytes from its \"ReadableStream\".\n *\n * This polyfill was pulled from https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nfunction readableStreamAsyncIterable(stream) {\n    if (stream[Symbol.asyncIterator])\n        return stream;\n    const reader = stream.getReader();\n    return {\n        async next() {\n            try {\n                const result = await reader.read();\n                if (result?.done)\n                    reader.releaseLock(); // release lock when stream becomes closed\n                return result;\n            }\n            catch (e) {\n                reader.releaseLock(); // release lock when stream becomes errored\n                throw e;\n            }\n        },\n        async return() {\n            const cancelPromise = reader.cancel();\n            reader.releaseLock();\n            await cancelPromise;\n            return { done: true, value: undefined };\n        },\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n    };\n}\n//# sourceMappingURL=streaming.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/lib/streaming.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resource.mjs":
/*!********************************************!*\
  !*** ./node_modules/groq-sdk/resource.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIResource: () => (/* binding */ APIResource)\n/* harmony export */ });\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nclass APIResource {\n    constructor(client) {\n        this._client = client;\n    }\n}\n//# sourceMappingURL=resource.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2UubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxXaWtpU2VhcmNoXFxub2RlX21vZHVsZXNcXGdyb3Etc2RrXFxyZXNvdXJjZS5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmV4cG9ydCBjbGFzcyBBUElSZXNvdXJjZSB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuX2NsaWVudCA9IGNsaWVudDtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZXNvdXJjZS5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resource.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/audio/audio.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/audio.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Audio: () => (/* binding */ Audio)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _speech_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./speech.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/audio/speech.mjs\");\n/* harmony import */ var _transcriptions_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transcriptions.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/audio/transcriptions.mjs\");\n/* harmony import */ var _translations_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./translations.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/audio/translations.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\n\n\n\n\n\nclass Audio extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    constructor() {\n        super(...arguments);\n        this.speech = new _speech_mjs__WEBPACK_IMPORTED_MODULE_1__.Speech(this._client);\n        this.transcriptions = new _transcriptions_mjs__WEBPACK_IMPORTED_MODULE_2__.Transcriptions(this._client);\n        this.translations = new _translations_mjs__WEBPACK_IMPORTED_MODULE_3__.Translations(this._client);\n    }\n}\nAudio.Speech = _speech_mjs__WEBPACK_IMPORTED_MODULE_1__.Speech;\nAudio.Transcriptions = _transcriptions_mjs__WEBPACK_IMPORTED_MODULE_2__.Transcriptions;\nAudio.Translations = _translations_mjs__WEBPACK_IMPORTED_MODULE_3__.Translations;\n//# sourceMappingURL=audio.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL2F1ZGlvLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ2lEO0FBQ1A7QUFDSjtBQUNvQjtBQUNKO0FBQ0E7QUFDSjtBQUMzQyxvQkFBb0Isc0RBQVc7QUFDdEM7QUFDQTtBQUNBLDBCQUEwQiwrQ0FBZ0I7QUFDMUMsa0NBQWtDLCtEQUFnQztBQUNsRSxnQ0FBZ0MsMkRBQTRCO0FBQzVEO0FBQ0E7QUFDQSxlQUFlLCtDQUFNO0FBQ3JCLHVCQUF1QiwrREFBYztBQUNyQyxxQkFBcUIsMkRBQVk7QUFDakMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcV2lraVNlYXJjaFxcbm9kZV9tb2R1bGVzXFxncm9xLXNka1xccmVzb3VyY2VzXFxhdWRpb1xcYXVkaW8ubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5pbXBvcnQgeyBBUElSZXNvdXJjZSB9IGZyb20gXCIuLi8uLi9yZXNvdXJjZS5tanNcIjtcbmltcG9ydCAqIGFzIFNwZWVjaEFQSSBmcm9tIFwiLi9zcGVlY2gubWpzXCI7XG5pbXBvcnQgeyBTcGVlY2ggfSBmcm9tIFwiLi9zcGVlY2gubWpzXCI7XG5pbXBvcnQgKiBhcyBUcmFuc2NyaXB0aW9uc0FQSSBmcm9tIFwiLi90cmFuc2NyaXB0aW9ucy5tanNcIjtcbmltcG9ydCB7IFRyYW5zY3JpcHRpb25zIH0gZnJvbSBcIi4vdHJhbnNjcmlwdGlvbnMubWpzXCI7XG5pbXBvcnQgKiBhcyBUcmFuc2xhdGlvbnNBUEkgZnJvbSBcIi4vdHJhbnNsYXRpb25zLm1qc1wiO1xuaW1wb3J0IHsgVHJhbnNsYXRpb25zIH0gZnJvbSBcIi4vdHJhbnNsYXRpb25zLm1qc1wiO1xuZXhwb3J0IGNsYXNzIEF1ZGlvIGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xuICAgICAgICB0aGlzLnNwZWVjaCA9IG5ldyBTcGVlY2hBUEkuU3BlZWNoKHRoaXMuX2NsaWVudCk7XG4gICAgICAgIHRoaXMudHJhbnNjcmlwdGlvbnMgPSBuZXcgVHJhbnNjcmlwdGlvbnNBUEkuVHJhbnNjcmlwdGlvbnModGhpcy5fY2xpZW50KTtcbiAgICAgICAgdGhpcy50cmFuc2xhdGlvbnMgPSBuZXcgVHJhbnNsYXRpb25zQVBJLlRyYW5zbGF0aW9ucyh0aGlzLl9jbGllbnQpO1xuICAgIH1cbn1cbkF1ZGlvLlNwZWVjaCA9IFNwZWVjaDtcbkF1ZGlvLlRyYW5zY3JpcHRpb25zID0gVHJhbnNjcmlwdGlvbnM7XG5BdWRpby5UcmFuc2xhdGlvbnMgPSBUcmFuc2xhdGlvbnM7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1hdWRpby5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/audio/audio.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/audio/speech.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/speech.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Speech: () => (/* binding */ Speech)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Speech extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Generates audio from the input text.\n     *\n     * @example\n     * ```ts\n     * const speech = await client.audio.speech.create({\n     *   input: 'The quick brown fox jumped over the lazy dog',\n     *   model: 'playai-tts',\n     *   voice: 'Fritz-PlayAI',\n     * });\n     *\n     * const content = await speech.blob();\n     * console.log(content);\n     * ```\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/audio/speech', {\n            body,\n            ...options,\n            headers: { Accept: 'audio/wav', ...options?.headers },\n            __binaryResponse: true,\n        });\n    }\n}\n//# sourceMappingURL=speech.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL3NwZWVjaC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNpRDtBQUMxQyxxQkFBcUIsc0RBQVc7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwwQ0FBMEM7QUFDakU7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFdpa2lTZWFyY2hcXG5vZGVfbW9kdWxlc1xcZ3JvcS1zZGtcXHJlc291cmNlc1xcYXVkaW9cXHNwZWVjaC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIFNwZWVjaCBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICAvKipcbiAgICAgKiBHZW5lcmF0ZXMgYXVkaW8gZnJvbSB0aGUgaW5wdXQgdGV4dC5cbiAgICAgKlxuICAgICAqIEBleGFtcGxlXG4gICAgICogYGBgdHNcbiAgICAgKiBjb25zdCBzcGVlY2ggPSBhd2FpdCBjbGllbnQuYXVkaW8uc3BlZWNoLmNyZWF0ZSh7XG4gICAgICogICBpbnB1dDogJ1RoZSBxdWljayBicm93biBmb3gganVtcGVkIG92ZXIgdGhlIGxhenkgZG9nJyxcbiAgICAgKiAgIG1vZGVsOiAncGxheWFpLXR0cycsXG4gICAgICogICB2b2ljZTogJ0ZyaXR6LVBsYXlBSScsXG4gICAgICogfSk7XG4gICAgICpcbiAgICAgKiBjb25zdCBjb250ZW50ID0gYXdhaXQgc3BlZWNoLmJsb2IoKTtcbiAgICAgKiBjb25zb2xlLmxvZyhjb250ZW50KTtcbiAgICAgKiBgYGBcbiAgICAgKi9cbiAgICBjcmVhdGUoYm9keSwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LnBvc3QoJy9vcGVuYWkvdjEvYXVkaW8vc3BlZWNoJywge1xuICAgICAgICAgICAgYm9keSxcbiAgICAgICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgICAgICBoZWFkZXJzOiB7IEFjY2VwdDogJ2F1ZGlvL3dhdicsIC4uLm9wdGlvbnM/LmhlYWRlcnMgfSxcbiAgICAgICAgICAgIF9fYmluYXJ5UmVzcG9uc2U6IHRydWUsXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNwZWVjaC5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/audio/speech.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/audio/transcriptions.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/transcriptions.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Transcriptions: () => (/* binding */ Transcriptions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core.mjs */ \"(ssr)/./node_modules/groq-sdk/uploads.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Transcriptions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Transcribes audio into the input language.\n     *\n     * @example\n     * ```ts\n     * const transcription =\n     *   await client.audio.transcriptions.create({\n     *     model: 'whisper-large-v3-turbo',\n     *   });\n     * ```\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/audio/transcriptions', _core_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions({ body, ...options }));\n    }\n}\n//# sourceMappingURL=transcriptions.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL3RyYW5zY3JpcHRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNpRDtBQUNWO0FBQ2hDLDZCQUE2QixzREFBVztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLG9FQUFvRSxrRUFBZ0MsR0FBRyxrQkFBa0I7QUFDekg7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFdpa2lTZWFyY2hcXG5vZGVfbW9kdWxlc1xcZ3JvcS1zZGtcXHJlc291cmNlc1xcYXVkaW9cXHRyYW5zY3JpcHRpb25zLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuaW1wb3J0IHsgQVBJUmVzb3VyY2UgfSBmcm9tIFwiLi4vLi4vcmVzb3VyY2UubWpzXCI7XG5pbXBvcnQgKiBhcyBDb3JlIGZyb20gXCIuLi8uLi9jb3JlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIFRyYW5zY3JpcHRpb25zIGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIC8qKlxuICAgICAqIFRyYW5zY3JpYmVzIGF1ZGlvIGludG8gdGhlIGlucHV0IGxhbmd1YWdlLlxuICAgICAqXG4gICAgICogQGV4YW1wbGVcbiAgICAgKiBgYGB0c1xuICAgICAqIGNvbnN0IHRyYW5zY3JpcHRpb24gPVxuICAgICAqICAgYXdhaXQgY2xpZW50LmF1ZGlvLnRyYW5zY3JpcHRpb25zLmNyZWF0ZSh7XG4gICAgICogICAgIG1vZGVsOiAnd2hpc3Blci1sYXJnZS12My10dXJibycsXG4gICAgICogICB9KTtcbiAgICAgKiBgYGBcbiAgICAgKi9cbiAgICBjcmVhdGUoYm9keSwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LnBvc3QoJy9vcGVuYWkvdjEvYXVkaW8vdHJhbnNjcmlwdGlvbnMnLCBDb3JlLm11bHRpcGFydEZvcm1SZXF1ZXN0T3B0aW9ucyh7IGJvZHksIC4uLm9wdGlvbnMgfSkpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyYW5zY3JpcHRpb25zLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/audio/transcriptions.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/audio/translations.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/translations.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Translations: () => (/* binding */ Translations)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core.mjs */ \"(ssr)/./node_modules/groq-sdk/uploads.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Translations extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Translates audio into English.\n     *\n     * @example\n     * ```ts\n     * const translation = await client.audio.translations.create({\n     *   model: 'whisper-large-v3-turbo',\n     * });\n     * ```\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/audio/translations', _core_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions({ body, ...options }));\n    }\n}\n//# sourceMappingURL=translations.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL3RyYW5zbGF0aW9ucy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDaUQ7QUFDVjtBQUNoQywyQkFBMkIsc0RBQVc7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0Esa0VBQWtFLGtFQUFnQyxHQUFHLGtCQUFrQjtBQUN2SDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcV2lraVNlYXJjaFxcbm9kZV9tb2R1bGVzXFxncm9xLXNka1xccmVzb3VyY2VzXFxhdWRpb1xcdHJhbnNsYXRpb25zLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuaW1wb3J0IHsgQVBJUmVzb3VyY2UgfSBmcm9tIFwiLi4vLi4vcmVzb3VyY2UubWpzXCI7XG5pbXBvcnQgKiBhcyBDb3JlIGZyb20gXCIuLi8uLi9jb3JlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIFRyYW5zbGF0aW9ucyBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICAvKipcbiAgICAgKiBUcmFuc2xhdGVzIGF1ZGlvIGludG8gRW5nbGlzaC5cbiAgICAgKlxuICAgICAqIEBleGFtcGxlXG4gICAgICogYGBgdHNcbiAgICAgKiBjb25zdCB0cmFuc2xhdGlvbiA9IGF3YWl0IGNsaWVudC5hdWRpby50cmFuc2xhdGlvbnMuY3JlYXRlKHtcbiAgICAgKiAgIG1vZGVsOiAnd2hpc3Blci1sYXJnZS12My10dXJibycsXG4gICAgICogfSk7XG4gICAgICogYGBgXG4gICAgICovXG4gICAgY3JlYXRlKGJvZHksIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5wb3N0KCcvb3BlbmFpL3YxL2F1ZGlvL3RyYW5zbGF0aW9ucycsIENvcmUubXVsdGlwYXJ0Rm9ybVJlcXVlc3RPcHRpb25zKHsgYm9keSwgLi4ub3B0aW9ucyB9KSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dHJhbnNsYXRpb25zLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/audio/translations.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/batches.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/groq-sdk/resources/batches.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Batches: () => (/* binding */ Batches)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Batches extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Creates and executes a batch from an uploaded file of requests.\n     * [Learn more](/docs/batch).\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/batches', { body, ...options });\n    }\n    /**\n     * Retrieves a batch.\n     */\n    retrieve(batchId, options) {\n        return this._client.get(`/openai/v1/batches/${batchId}`, options);\n    }\n    /**\n     * List your organization's batches.\n     */\n    list(options) {\n        return this._client.get('/openai/v1/batches', options);\n    }\n    /**\n     * Cancels a batch.\n     */\n    cancel(batchId, options) {\n        return this._client.post(`/openai/v1/batches/${batchId}/cancel`, options);\n    }\n}\n//# sourceMappingURL=batches.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2JhdGNoZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDOEM7QUFDdkMsc0JBQXNCLHNEQUFXO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5REFBeUQsa0JBQWtCO0FBQzNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsUUFBUTtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVELFFBQVE7QUFDL0Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFdpa2lTZWFyY2hcXG5vZGVfbW9kdWxlc1xcZ3JvcS1zZGtcXHJlc291cmNlc1xcYmF0Y2hlcy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uL3Jlc291cmNlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIEJhdGNoZXMgZXh0ZW5kcyBBUElSZXNvdXJjZSB7XG4gICAgLyoqXG4gICAgICogQ3JlYXRlcyBhbmQgZXhlY3V0ZXMgYSBiYXRjaCBmcm9tIGFuIHVwbG9hZGVkIGZpbGUgb2YgcmVxdWVzdHMuXG4gICAgICogW0xlYXJuIG1vcmVdKC9kb2NzL2JhdGNoKS5cbiAgICAgKi9cbiAgICBjcmVhdGUoYm9keSwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LnBvc3QoJy9vcGVuYWkvdjEvYmF0Y2hlcycsIHsgYm9keSwgLi4ub3B0aW9ucyB9KTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmV0cmlldmVzIGEgYmF0Y2guXG4gICAgICovXG4gICAgcmV0cmlldmUoYmF0Y2hJZCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmdldChgL29wZW5haS92MS9iYXRjaGVzLyR7YmF0Y2hJZH1gLCBvcHRpb25zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogTGlzdCB5b3VyIG9yZ2FuaXphdGlvbidzIGJhdGNoZXMuXG4gICAgICovXG4gICAgbGlzdChvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQuZ2V0KCcvb3BlbmFpL3YxL2JhdGNoZXMnLCBvcHRpb25zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2FuY2VscyBhIGJhdGNoLlxuICAgICAqL1xuICAgIGNhbmNlbChiYXRjaElkLCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQucG9zdChgL29wZW5haS92MS9iYXRjaGVzLyR7YmF0Y2hJZH0vY2FuY2VsYCwgb3B0aW9ucyk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YmF0Y2hlcy5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/batches.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/chat/chat.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/groq-sdk/resources/chat/chat.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chat: () => (/* binding */ Chat)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _completions_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./completions.mjs */ \"(ssr)/./node_modules/groq-sdk/resources/chat/completions.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\n\nclass Chat extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    constructor() {\n        super(...arguments);\n        this.completions = new _completions_mjs__WEBPACK_IMPORTED_MODULE_1__.Completions(this._client);\n    }\n}\nChat.Completions = _completions_mjs__WEBPACK_IMPORTED_MODULE_1__.Completions;\n//# sourceMappingURL=chat.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NoYXQvY2hhdC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDaUQ7QUFDRztBQUNIO0FBQzFDLG1CQUFtQixzREFBVztBQUNyQztBQUNBO0FBQ0EsK0JBQStCLHlEQUEwQjtBQUN6RDtBQUNBO0FBQ0EsbUJBQW1CLHlEQUFXO0FBQzlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFdpa2lTZWFyY2hcXG5vZGVfbW9kdWxlc1xcZ3JvcS1zZGtcXHJlc291cmNlc1xcY2hhdFxcY2hhdC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuaW1wb3J0ICogYXMgQ29tcGxldGlvbnNBUEkgZnJvbSBcIi4vY29tcGxldGlvbnMubWpzXCI7XG5pbXBvcnQgeyBDb21wbGV0aW9ucywgfSBmcm9tIFwiLi9jb21wbGV0aW9ucy5tanNcIjtcbmV4cG9ydCBjbGFzcyBDaGF0IGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xuICAgICAgICB0aGlzLmNvbXBsZXRpb25zID0gbmV3IENvbXBsZXRpb25zQVBJLkNvbXBsZXRpb25zKHRoaXMuX2NsaWVudCk7XG4gICAgfVxufVxuQ2hhdC5Db21wbGV0aW9ucyA9IENvbXBsZXRpb25zO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2hhdC5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/chat/chat.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/chat/completions.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/groq-sdk/resources/chat/completions.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Completions: () => (/* binding */ Completions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Completions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    create(body, options) {\n        return this._client.post('/openai/v1/chat/completions', {\n            body,\n            ...options,\n            stream: body.stream ?? false,\n        });\n    }\n}\n//# sourceMappingURL=completions.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NoYXQvY29tcGxldGlvbnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDaUQ7QUFDMUMsMEJBQTBCLHNEQUFXO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFdpa2lTZWFyY2hcXG5vZGVfbW9kdWxlc1xcZ3JvcS1zZGtcXHJlc291cmNlc1xcY2hhdFxcY29tcGxldGlvbnMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5pbXBvcnQgeyBBUElSZXNvdXJjZSB9IGZyb20gXCIuLi8uLi9yZXNvdXJjZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBDb21wbGV0aW9ucyBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICBjcmVhdGUoYm9keSwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LnBvc3QoJy9vcGVuYWkvdjEvY2hhdC9jb21wbGV0aW9ucycsIHtcbiAgICAgICAgICAgIGJvZHksXG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgc3RyZWFtOiBib2R5LnN0cmVhbSA/PyBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29tcGxldGlvbnMubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/chat/completions.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/completions.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/completions.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Completions: () => (/* binding */ Completions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Completions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n}\n//# sourceMappingURL=completions.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NvbXBsZXRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQzhDO0FBQ3ZDLDBCQUEwQixzREFBVztBQUM1QztBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFdpa2lTZWFyY2hcXG5vZGVfbW9kdWxlc1xcZ3JvcS1zZGtcXHJlc291cmNlc1xcY29tcGxldGlvbnMubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5pbXBvcnQgeyBBUElSZXNvdXJjZSB9IGZyb20gXCIuLi9yZXNvdXJjZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBDb21wbGV0aW9ucyBleHRlbmRzIEFQSVJlc291cmNlIHtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbXBsZXRpb25zLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/completions.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/embeddings.mjs":
/*!********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/embeddings.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Embeddings: () => (/* binding */ Embeddings)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Embeddings extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Creates an embedding vector representing the input text.\n     *\n     * @example\n     * ```ts\n     * const createEmbeddingResponse =\n     *   await client.embeddings.create({\n     *     input: 'The quick brown fox jumped over the lazy dog',\n     *     model: 'nomic-embed-text-v1_5',\n     *   });\n     * ```\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/embeddings', { body, ...options });\n    }\n}\n//# sourceMappingURL=embeddings.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2VtYmVkZGluZ3MubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDOEM7QUFDdkMseUJBQXlCLHNEQUFXO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsa0JBQWtCO0FBQzlFO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxXaWtpU2VhcmNoXFxub2RlX21vZHVsZXNcXGdyb3Etc2RrXFxyZXNvdXJjZXNcXGVtYmVkZGluZ3MubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5pbXBvcnQgeyBBUElSZXNvdXJjZSB9IGZyb20gXCIuLi9yZXNvdXJjZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBFbWJlZGRpbmdzIGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIC8qKlxuICAgICAqIENyZWF0ZXMgYW4gZW1iZWRkaW5nIHZlY3RvciByZXByZXNlbnRpbmcgdGhlIGlucHV0IHRleHQuXG4gICAgICpcbiAgICAgKiBAZXhhbXBsZVxuICAgICAqIGBgYHRzXG4gICAgICogY29uc3QgY3JlYXRlRW1iZWRkaW5nUmVzcG9uc2UgPVxuICAgICAqICAgYXdhaXQgY2xpZW50LmVtYmVkZGluZ3MuY3JlYXRlKHtcbiAgICAgKiAgICAgaW5wdXQ6ICdUaGUgcXVpY2sgYnJvd24gZm94IGp1bXBlZCBvdmVyIHRoZSBsYXp5IGRvZycsXG4gICAgICogICAgIG1vZGVsOiAnbm9taWMtZW1iZWQtdGV4dC12MV81JyxcbiAgICAgKiAgIH0pO1xuICAgICAqIGBgYFxuICAgICAqL1xuICAgIGNyZWF0ZShib2R5LCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQucG9zdCgnL29wZW5haS92MS9lbWJlZGRpbmdzJywgeyBib2R5LCAuLi5vcHRpb25zIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVtYmVkZGluZ3MubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/embeddings.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/files.mjs":
/*!***************************************************!*\
  !*** ./node_modules/groq-sdk/resources/files.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Files: () => (/* binding */ Files)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core.mjs */ \"(ssr)/./node_modules/groq-sdk/uploads.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Files extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Upload a file that can be used across various endpoints.\n     *\n     * The Batch API only supports `.jsonl` files up to 100 MB in size. The input also\n     * has a specific required [format](/docs/batch).\n     *\n     * Please contact us if you need to increase these storage limits.\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/files', _core_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions({ body, ...options }));\n    }\n    /**\n     * Returns a list of files.\n     */\n    list(options) {\n        return this._client.get('/openai/v1/files', options);\n    }\n    /**\n     * Delete a file.\n     */\n    delete(fileId, options) {\n        return this._client.delete(`/openai/v1/files/${fileId}`, options);\n    }\n    /**\n     * Returns the contents of the specified file.\n     */\n    content(fileId, options) {\n        return this._client.get(`/openai/v1/files/${fileId}/content`, {\n            ...options,\n            headers: { Accept: 'application/octet-stream', ...options?.headers },\n            __binaryResponse: true,\n        });\n    }\n    /**\n     * Returns information about a file.\n     */\n    info(fileId, options) {\n        return this._client.get(`/openai/v1/files/${fileId}`, options);\n    }\n}\n//# sourceMappingURL=files.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2ZpbGVzLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUM4QztBQUNWO0FBQzdCLG9CQUFvQixzREFBVztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsa0VBQWdDLEdBQUcsa0JBQWtCO0FBQzFHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1REFBdUQsT0FBTztBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0RBQW9ELE9BQU87QUFDM0Q7QUFDQSx1QkFBdUIseURBQXlEO0FBQ2hGO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvREFBb0QsT0FBTztBQUMzRDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcdXNlclxcRGVza3RvcFxcV2lraVNlYXJjaFxcbm9kZV9tb2R1bGVzXFxncm9xLXNka1xccmVzb3VyY2VzXFxmaWxlcy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uL3Jlc291cmNlLm1qc1wiO1xuaW1wb3J0ICogYXMgQ29yZSBmcm9tIFwiLi4vY29yZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBGaWxlcyBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICAvKipcbiAgICAgKiBVcGxvYWQgYSBmaWxlIHRoYXQgY2FuIGJlIHVzZWQgYWNyb3NzIHZhcmlvdXMgZW5kcG9pbnRzLlxuICAgICAqXG4gICAgICogVGhlIEJhdGNoIEFQSSBvbmx5IHN1cHBvcnRzIGAuanNvbmxgIGZpbGVzIHVwIHRvIDEwMCBNQiBpbiBzaXplLiBUaGUgaW5wdXQgYWxzb1xuICAgICAqIGhhcyBhIHNwZWNpZmljIHJlcXVpcmVkIFtmb3JtYXRdKC9kb2NzL2JhdGNoKS5cbiAgICAgKlxuICAgICAqIFBsZWFzZSBjb250YWN0IHVzIGlmIHlvdSBuZWVkIHRvIGluY3JlYXNlIHRoZXNlIHN0b3JhZ2UgbGltaXRzLlxuICAgICAqL1xuICAgIGNyZWF0ZShib2R5LCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQucG9zdCgnL29wZW5haS92MS9maWxlcycsIENvcmUubXVsdGlwYXJ0Rm9ybVJlcXVlc3RPcHRpb25zKHsgYm9keSwgLi4ub3B0aW9ucyB9KSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJldHVybnMgYSBsaXN0IG9mIGZpbGVzLlxuICAgICAqL1xuICAgIGxpc3Qob3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmdldCgnL29wZW5haS92MS9maWxlcycsIG9wdGlvbnMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEZWxldGUgYSBmaWxlLlxuICAgICAqL1xuICAgIGRlbGV0ZShmaWxlSWQsIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5kZWxldGUoYC9vcGVuYWkvdjEvZmlsZXMvJHtmaWxlSWR9YCwgb3B0aW9ucyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIGNvbnRlbnRzIG9mIHRoZSBzcGVjaWZpZWQgZmlsZS5cbiAgICAgKi9cbiAgICBjb250ZW50KGZpbGVJZCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmdldChgL29wZW5haS92MS9maWxlcy8ke2ZpbGVJZH0vY29udGVudGAsIHtcbiAgICAgICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgICAgICBoZWFkZXJzOiB7IEFjY2VwdDogJ2FwcGxpY2F0aW9uL29jdGV0LXN0cmVhbScsIC4uLm9wdGlvbnM/LmhlYWRlcnMgfSxcbiAgICAgICAgICAgIF9fYmluYXJ5UmVzcG9uc2U6IHRydWUsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXR1cm5zIGluZm9ybWF0aW9uIGFib3V0IGEgZmlsZS5cbiAgICAgKi9cbiAgICBpbmZvKGZpbGVJZCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmdldChgL29wZW5haS92MS9maWxlcy8ke2ZpbGVJZH1gLCBvcHRpb25zKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1maWxlcy5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/files.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/resources/models.mjs":
/*!****************************************************!*\
  !*** ./node_modules/groq-sdk/resources/models.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Models: () => (/* binding */ Models)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(ssr)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Models extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Get a specific model\n     */\n    retrieve(model, options) {\n        return this._client.get(`/openai/v1/models/${model}`, options);\n    }\n    /**\n     * get all available models\n     */\n    list(options) {\n        return this._client.get('/openai/v1/models', options);\n    }\n    /**\n     * Delete a model\n     */\n    delete(model, options) {\n        return this._client.delete(`/openai/v1/models/${model}`, options);\n    }\n}\n//# sourceMappingURL=models.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL21vZGVscy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUM4QztBQUN2QyxxQkFBcUIsc0RBQVc7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsTUFBTTtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELE1BQU07QUFDOUQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHVzZXJcXERlc2t0b3BcXFdpa2lTZWFyY2hcXG5vZGVfbW9kdWxlc1xcZ3JvcS1zZGtcXHJlc291cmNlc1xcbW9kZWxzLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuaW1wb3J0IHsgQVBJUmVzb3VyY2UgfSBmcm9tIFwiLi4vcmVzb3VyY2UubWpzXCI7XG5leHBvcnQgY2xhc3MgTW9kZWxzIGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xuICAgIC8qKlxuICAgICAqIEdldCBhIHNwZWNpZmljIG1vZGVsXG4gICAgICovXG4gICAgcmV0cmlldmUobW9kZWwsIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5nZXQoYC9vcGVuYWkvdjEvbW9kZWxzLyR7bW9kZWx9YCwgb3B0aW9ucyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIGdldCBhbGwgYXZhaWxhYmxlIG1vZGVsc1xuICAgICAqL1xuICAgIGxpc3Qob3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmdldCgnL29wZW5haS92MS9tb2RlbHMnLCBvcHRpb25zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgbW9kZWxcbiAgICAgKi9cbiAgICBkZWxldGUobW9kZWwsIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5kZWxldGUoYC9vcGVuYWkvdjEvbW9kZWxzLyR7bW9kZWx9YCwgb3B0aW9ucyk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bW9kZWxzLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/resources/models.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/uploads.mjs":
/*!*******************************************!*\
  !*** ./node_modules/groq-sdk/uploads.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createForm: () => (/* binding */ createForm),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFromPath),\n/* harmony export */   isBlobLike: () => (/* binding */ isBlobLike),\n/* harmony export */   isFileLike: () => (/* binding */ isFileLike),\n/* harmony export */   isMultipartBody: () => (/* binding */ isMultipartBody),\n/* harmony export */   isResponseLike: () => (/* binding */ isResponseLike),\n/* harmony export */   isUploadable: () => (/* binding */ isUploadable),\n/* harmony export */   maybeMultipartFormRequestOptions: () => (/* binding */ maybeMultipartFormRequestOptions),\n/* harmony export */   multipartFormRequestOptions: () => (/* binding */ multipartFormRequestOptions),\n/* harmony export */   toFile: () => (/* binding */ toFile)\n/* harmony export */ });\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_shims/index.mjs */ \"(ssr)/./node_modules/groq-sdk/_shims/index.mjs\");\n\n\nconst isResponseLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.url === 'string' &&\n    typeof value.blob === 'function';\nconst isFileLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.name === 'string' &&\n    typeof value.lastModified === 'number' &&\n    isBlobLike(value);\n/**\n * The BlobLike type omits arrayBuffer() because @types/node-fetch@^2.6.4 lacks it; but this check\n * adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isBlobLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.size === 'number' &&\n    typeof value.type === 'string' &&\n    typeof value.text === 'function' &&\n    typeof value.slice === 'function' &&\n    typeof value.arrayBuffer === 'function';\nconst isUploadable = (value) => {\n    return isFileLike(value) || isResponseLike(value) || (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.isFsReadStream)(value);\n};\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */\nasync function toFile(value, name, options) {\n    // If it's a promise, resolve it.\n    value = await value;\n    // If we've been given a `File` we don't need to do anything\n    if (isFileLike(value)) {\n        return value;\n    }\n    if (isResponseLike(value)) {\n        const blob = await value.blob();\n        name || (name = new URL(value.url).pathname.split(/[\\\\/]/).pop() ?? 'unknown_file');\n        // we need to convert the `Blob` into an array buffer because the `Blob` class\n        // that `node-fetch` defines is incompatible with the web standard which results\n        // in `new File` interpreting it as a string instead of binary data.\n        const data = isBlobLike(blob) ? [(await blob.arrayBuffer())] : [blob];\n        return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.File(data, name, options);\n    }\n    const bits = await getBytes(value);\n    name || (name = getName(value) ?? 'unknown_file');\n    if (!options?.type) {\n        const type = bits[0]?.type;\n        if (typeof type === 'string') {\n            options = { ...options, type };\n        }\n    }\n    return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.File(bits, name, options);\n}\nasync function getBytes(value) {\n    let parts = [];\n    if (typeof value === 'string' ||\n        ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n        value instanceof ArrayBuffer) {\n        parts.push(value);\n    }\n    else if (isBlobLike(value)) {\n        parts.push(await value.arrayBuffer());\n    }\n    else if (isAsyncIterableIterator(value) // includes Readable, ReadableStream, etc.\n    ) {\n        for await (const chunk of value) {\n            parts.push(chunk); // TODO, consider validating?\n        }\n    }\n    else {\n        throw new Error(`Unexpected data type: ${typeof value}; constructor: ${value?.constructor\n            ?.name}; props: ${propsForError(value)}`);\n    }\n    return parts;\n}\nfunction propsForError(value) {\n    const props = Object.getOwnPropertyNames(value);\n    return `[${props.map((p) => `\"${p}\"`).join(', ')}]`;\n}\nfunction getName(value) {\n    return (getStringFromMaybeBuffer(value.name) ||\n        getStringFromMaybeBuffer(value.filename) ||\n        // For fs.ReadStream\n        getStringFromMaybeBuffer(value.path)?.split(/[\\\\/]/).pop());\n}\nconst getStringFromMaybeBuffer = (x) => {\n    if (typeof x === 'string')\n        return x;\n    if (typeof Buffer !== 'undefined' && x instanceof Buffer)\n        return String(x);\n    return undefined;\n};\nconst isAsyncIterableIterator = (value) => value != null && typeof value === 'object' && typeof value[Symbol.asyncIterator] === 'function';\nconst isMultipartBody = (body) => body && typeof body === 'object' && body.body && body[Symbol.toStringTag] === 'MultipartBody';\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */\nconst maybeMultipartFormRequestOptions = async (opts) => {\n    if (!hasUploadableValue(opts.body))\n        return opts;\n    const form = await createForm(opts.body);\n    return (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions)(form, opts);\n};\nconst multipartFormRequestOptions = async (opts) => {\n    const form = await createForm(opts.body);\n    return (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions)(form, opts);\n};\nconst createForm = async (body) => {\n    const form = new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FormData();\n    await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));\n    return form;\n};\nconst hasUploadableValue = (value) => {\n    if (isUploadable(value))\n        return true;\n    if (Array.isArray(value))\n        return value.some(hasUploadableValue);\n    if (value && typeof value === 'object') {\n        for (const k in value) {\n            if (hasUploadableValue(value[k]))\n                return true;\n        }\n    }\n    return false;\n};\nconst addFormValue = async (form, key, value) => {\n    if (value === undefined)\n        return;\n    if (value == null) {\n        throw new TypeError(`Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`);\n    }\n    // TODO: make nested formats configurable\n    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n        form.append(key, String(value));\n    }\n    else if (isUploadable(value)) {\n        const file = await toFile(value);\n        form.append(key, file);\n    }\n    else if (Array.isArray(value)) {\n        await Promise.all(value.map((entry) => addFormValue(form, key + '[]', entry)));\n    }\n    else if (typeof value === 'object') {\n        await Promise.all(Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)));\n    }\n    else {\n        throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`);\n    }\n};\n//# sourceMappingURL=uploads.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/uploads.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/groq-sdk/version.mjs":
/*!*******************************************!*\
  !*** ./node_modules/groq-sdk/version.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = '0.22.0'; // x-release-please-version\n//# sourceMappingURL=version.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvdmVyc2lvbi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLDBCQUEwQjtBQUNqQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx1c2VyXFxEZXNrdG9wXFxXaWtpU2VhcmNoXFxub2RlX21vZHVsZXNcXGdyb3Etc2RrXFx2ZXJzaW9uLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVkVSU0lPTiA9ICcwLjIyLjAnOyAvLyB4LXJlbGVhc2UtcGxlYXNlLXZlcnNpb25cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24ubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/groq-sdk/version.mjs\n");

/***/ })

};
;