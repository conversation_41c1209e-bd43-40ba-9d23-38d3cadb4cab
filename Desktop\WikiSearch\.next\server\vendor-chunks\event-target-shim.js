"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/event-target-shim";
exports.ids = ["vendor-chunks/event-target-shim"];
exports.modules = {

/***/ "(ssr)/./node_modules/event-target-shim/dist/event-target-shim.js":
/*!******************************************************************!*\
  !*** ./node_modules/event-target-shim/dist/event-target-shim.js ***!
  \******************************************************************/
/***/ ((module, exports) => {

eval("/**\n * <AUTHOR> Nagashima <https://github.com/mysticatea>\n * @copyright 2015 Toru Nagashima. All rights reserved.\n * See LICENSE file in root directory for full license.\n */\n\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\n/**\n * @typedef {object} PrivateData\n * @property {EventTarget} eventTarget The event target.\n * @property {{type:string}} event The original event object.\n * @property {number} eventPhase The current event phase.\n * @property {EventTarget|null} currentTarget The current event target.\n * @property {boolean} canceled The flag to prevent default.\n * @property {boolean} stopped The flag to stop propagation.\n * @property {boolean} immediateStopped The flag to stop propagation immediately.\n * @property {Function|null} passiveListener The listener if the current listener is passive. Otherwise this is null.\n * @property {number} timeStamp The unix time.\n * @private\n */\n\n/**\n * Private data for event wrappers.\n * @type {WeakMap<Event, PrivateData>}\n * @private\n */\nconst privateData = new WeakMap();\n\n/**\n * Cache for wrapper classes.\n * @type {WeakMap<Object, Function>}\n * @private\n */\nconst wrappers = new WeakMap();\n\n/**\n * Get private data.\n * @param {Event} event The event object to get private data.\n * @returns {PrivateData} The private data of the event.\n * @private\n */\nfunction pd(event) {\n    const retv = privateData.get(event);\n    console.assert(\n        retv != null,\n        \"'this' is expected an Event object, but got\",\n        event\n    );\n    return retv\n}\n\n/**\n * https://dom.spec.whatwg.org/#set-the-canceled-flag\n * @param data {PrivateData} private data.\n */\nfunction setCancelFlag(data) {\n    if (data.passiveListener != null) {\n        if (\n            typeof console !== \"undefined\" &&\n            typeof console.error === \"function\"\n        ) {\n            console.error(\n                \"Unable to preventDefault inside passive event listener invocation.\",\n                data.passiveListener\n            );\n        }\n        return\n    }\n    if (!data.event.cancelable) {\n        return\n    }\n\n    data.canceled = true;\n    if (typeof data.event.preventDefault === \"function\") {\n        data.event.preventDefault();\n    }\n}\n\n/**\n * @see https://dom.spec.whatwg.org/#interface-event\n * @private\n */\n/**\n * The event wrapper.\n * @constructor\n * @param {EventTarget} eventTarget The event target of this dispatching.\n * @param {Event|{type:string}} event The original event to wrap.\n */\nfunction Event(eventTarget, event) {\n    privateData.set(this, {\n        eventTarget,\n        event,\n        eventPhase: 2,\n        currentTarget: eventTarget,\n        canceled: false,\n        stopped: false,\n        immediateStopped: false,\n        passiveListener: null,\n        timeStamp: event.timeStamp || Date.now(),\n    });\n\n    // https://heycam.github.io/webidl/#Unforgeable\n    Object.defineProperty(this, \"isTrusted\", { value: false, enumerable: true });\n\n    // Define accessors\n    const keys = Object.keys(event);\n    for (let i = 0; i < keys.length; ++i) {\n        const key = keys[i];\n        if (!(key in this)) {\n            Object.defineProperty(this, key, defineRedirectDescriptor(key));\n        }\n    }\n}\n\n// Should be enumerable, but class methods are not enumerable.\nEvent.prototype = {\n    /**\n     * The type of this event.\n     * @type {string}\n     */\n    get type() {\n        return pd(this).event.type\n    },\n\n    /**\n     * The target of this event.\n     * @type {EventTarget}\n     */\n    get target() {\n        return pd(this).eventTarget\n    },\n\n    /**\n     * The target of this event.\n     * @type {EventTarget}\n     */\n    get currentTarget() {\n        return pd(this).currentTarget\n    },\n\n    /**\n     * @returns {EventTarget[]} The composed path of this event.\n     */\n    composedPath() {\n        const currentTarget = pd(this).currentTarget;\n        if (currentTarget == null) {\n            return []\n        }\n        return [currentTarget]\n    },\n\n    /**\n     * Constant of NONE.\n     * @type {number}\n     */\n    get NONE() {\n        return 0\n    },\n\n    /**\n     * Constant of CAPTURING_PHASE.\n     * @type {number}\n     */\n    get CAPTURING_PHASE() {\n        return 1\n    },\n\n    /**\n     * Constant of AT_TARGET.\n     * @type {number}\n     */\n    get AT_TARGET() {\n        return 2\n    },\n\n    /**\n     * Constant of BUBBLING_PHASE.\n     * @type {number}\n     */\n    get BUBBLING_PHASE() {\n        return 3\n    },\n\n    /**\n     * The target of this event.\n     * @type {number}\n     */\n    get eventPhase() {\n        return pd(this).eventPhase\n    },\n\n    /**\n     * Stop event bubbling.\n     * @returns {void}\n     */\n    stopPropagation() {\n        const data = pd(this);\n\n        data.stopped = true;\n        if (typeof data.event.stopPropagation === \"function\") {\n            data.event.stopPropagation();\n        }\n    },\n\n    /**\n     * Stop event bubbling.\n     * @returns {void}\n     */\n    stopImmediatePropagation() {\n        const data = pd(this);\n\n        data.stopped = true;\n        data.immediateStopped = true;\n        if (typeof data.event.stopImmediatePropagation === \"function\") {\n            data.event.stopImmediatePropagation();\n        }\n    },\n\n    /**\n     * The flag to be bubbling.\n     * @type {boolean}\n     */\n    get bubbles() {\n        return Boolean(pd(this).event.bubbles)\n    },\n\n    /**\n     * The flag to be cancelable.\n     * @type {boolean}\n     */\n    get cancelable() {\n        return Boolean(pd(this).event.cancelable)\n    },\n\n    /**\n     * Cancel this event.\n     * @returns {void}\n     */\n    preventDefault() {\n        setCancelFlag(pd(this));\n    },\n\n    /**\n     * The flag to indicate cancellation state.\n     * @type {boolean}\n     */\n    get defaultPrevented() {\n        return pd(this).canceled\n    },\n\n    /**\n     * The flag to be composed.\n     * @type {boolean}\n     */\n    get composed() {\n        return Boolean(pd(this).event.composed)\n    },\n\n    /**\n     * The unix time of this event.\n     * @type {number}\n     */\n    get timeStamp() {\n        return pd(this).timeStamp\n    },\n\n    /**\n     * The target of this event.\n     * @type {EventTarget}\n     * @deprecated\n     */\n    get srcElement() {\n        return pd(this).eventTarget\n    },\n\n    /**\n     * The flag to stop event bubbling.\n     * @type {boolean}\n     * @deprecated\n     */\n    get cancelBubble() {\n        return pd(this).stopped\n    },\n    set cancelBubble(value) {\n        if (!value) {\n            return\n        }\n        const data = pd(this);\n\n        data.stopped = true;\n        if (typeof data.event.cancelBubble === \"boolean\") {\n            data.event.cancelBubble = true;\n        }\n    },\n\n    /**\n     * The flag to indicate cancellation state.\n     * @type {boolean}\n     * @deprecated\n     */\n    get returnValue() {\n        return !pd(this).canceled\n    },\n    set returnValue(value) {\n        if (!value) {\n            setCancelFlag(pd(this));\n        }\n    },\n\n    /**\n     * Initialize this event object. But do nothing under event dispatching.\n     * @param {string} type The event type.\n     * @param {boolean} [bubbles=false] The flag to be possible to bubble up.\n     * @param {boolean} [cancelable=false] The flag to be possible to cancel.\n     * @deprecated\n     */\n    initEvent() {\n        // Do nothing.\n    },\n};\n\n// `constructor` is not enumerable.\nObject.defineProperty(Event.prototype, \"constructor\", {\n    value: Event,\n    configurable: true,\n    writable: true,\n});\n\n// Ensure `event instanceof window.Event` is `true`.\nif (typeof window !== \"undefined\" && typeof window.Event !== \"undefined\") {\n    Object.setPrototypeOf(Event.prototype, window.Event.prototype);\n\n    // Make association for wrappers.\n    wrappers.set(window.Event.prototype, Event);\n}\n\n/**\n * Get the property descriptor to redirect a given property.\n * @param {string} key Property name to define property descriptor.\n * @returns {PropertyDescriptor} The property descriptor to redirect the property.\n * @private\n */\nfunction defineRedirectDescriptor(key) {\n    return {\n        get() {\n            return pd(this).event[key]\n        },\n        set(value) {\n            pd(this).event[key] = value;\n        },\n        configurable: true,\n        enumerable: true,\n    }\n}\n\n/**\n * Get the property descriptor to call a given method property.\n * @param {string} key Property name to define property descriptor.\n * @returns {PropertyDescriptor} The property descriptor to call the method property.\n * @private\n */\nfunction defineCallDescriptor(key) {\n    return {\n        value() {\n            const event = pd(this).event;\n            return event[key].apply(event, arguments)\n        },\n        configurable: true,\n        enumerable: true,\n    }\n}\n\n/**\n * Define new wrapper class.\n * @param {Function} BaseEvent The base wrapper class.\n * @param {Object} proto The prototype of the original event.\n * @returns {Function} The defined wrapper class.\n * @private\n */\nfunction defineWrapper(BaseEvent, proto) {\n    const keys = Object.keys(proto);\n    if (keys.length === 0) {\n        return BaseEvent\n    }\n\n    /** CustomEvent */\n    function CustomEvent(eventTarget, event) {\n        BaseEvent.call(this, eventTarget, event);\n    }\n\n    CustomEvent.prototype = Object.create(BaseEvent.prototype, {\n        constructor: { value: CustomEvent, configurable: true, writable: true },\n    });\n\n    // Define accessors.\n    for (let i = 0; i < keys.length; ++i) {\n        const key = keys[i];\n        if (!(key in BaseEvent.prototype)) {\n            const descriptor = Object.getOwnPropertyDescriptor(proto, key);\n            const isFunc = typeof descriptor.value === \"function\";\n            Object.defineProperty(\n                CustomEvent.prototype,\n                key,\n                isFunc\n                    ? defineCallDescriptor(key)\n                    : defineRedirectDescriptor(key)\n            );\n        }\n    }\n\n    return CustomEvent\n}\n\n/**\n * Get the wrapper class of a given prototype.\n * @param {Object} proto The prototype of the original event to get its wrapper.\n * @returns {Function} The wrapper class.\n * @private\n */\nfunction getWrapper(proto) {\n    if (proto == null || proto === Object.prototype) {\n        return Event\n    }\n\n    let wrapper = wrappers.get(proto);\n    if (wrapper == null) {\n        wrapper = defineWrapper(getWrapper(Object.getPrototypeOf(proto)), proto);\n        wrappers.set(proto, wrapper);\n    }\n    return wrapper\n}\n\n/**\n * Wrap a given event to management a dispatching.\n * @param {EventTarget} eventTarget The event target of this dispatching.\n * @param {Object} event The event to wrap.\n * @returns {Event} The wrapper instance.\n * @private\n */\nfunction wrapEvent(eventTarget, event) {\n    const Wrapper = getWrapper(Object.getPrototypeOf(event));\n    return new Wrapper(eventTarget, event)\n}\n\n/**\n * Get the immediateStopped flag of a given event.\n * @param {Event} event The event to get.\n * @returns {boolean} The flag to stop propagation immediately.\n * @private\n */\nfunction isStopped(event) {\n    return pd(event).immediateStopped\n}\n\n/**\n * Set the current event phase of a given event.\n * @param {Event} event The event to set current target.\n * @param {number} eventPhase New event phase.\n * @returns {void}\n * @private\n */\nfunction setEventPhase(event, eventPhase) {\n    pd(event).eventPhase = eventPhase;\n}\n\n/**\n * Set the current target of a given event.\n * @param {Event} event The event to set current target.\n * @param {EventTarget|null} currentTarget New current target.\n * @returns {void}\n * @private\n */\nfunction setCurrentTarget(event, currentTarget) {\n    pd(event).currentTarget = currentTarget;\n}\n\n/**\n * Set a passive listener of a given event.\n * @param {Event} event The event to set current target.\n * @param {Function|null} passiveListener New passive listener.\n * @returns {void}\n * @private\n */\nfunction setPassiveListener(event, passiveListener) {\n    pd(event).passiveListener = passiveListener;\n}\n\n/**\n * @typedef {object} ListenerNode\n * @property {Function} listener\n * @property {1|2|3} listenerType\n * @property {boolean} passive\n * @property {boolean} once\n * @property {ListenerNode|null} next\n * @private\n */\n\n/**\n * @type {WeakMap<object, Map<string, ListenerNode>>}\n * @private\n */\nconst listenersMap = new WeakMap();\n\n// Listener types\nconst CAPTURE = 1;\nconst BUBBLE = 2;\nconst ATTRIBUTE = 3;\n\n/**\n * Check whether a given value is an object or not.\n * @param {any} x The value to check.\n * @returns {boolean} `true` if the value is an object.\n */\nfunction isObject(x) {\n    return x !== null && typeof x === \"object\" //eslint-disable-line no-restricted-syntax\n}\n\n/**\n * Get listeners.\n * @param {EventTarget} eventTarget The event target to get.\n * @returns {Map<string, ListenerNode>} The listeners.\n * @private\n */\nfunction getListeners(eventTarget) {\n    const listeners = listenersMap.get(eventTarget);\n    if (listeners == null) {\n        throw new TypeError(\n            \"'this' is expected an EventTarget object, but got another value.\"\n        )\n    }\n    return listeners\n}\n\n/**\n * Get the property descriptor for the event attribute of a given event.\n * @param {string} eventName The event name to get property descriptor.\n * @returns {PropertyDescriptor} The property descriptor.\n * @private\n */\nfunction defineEventAttributeDescriptor(eventName) {\n    return {\n        get() {\n            const listeners = getListeners(this);\n            let node = listeners.get(eventName);\n            while (node != null) {\n                if (node.listenerType === ATTRIBUTE) {\n                    return node.listener\n                }\n                node = node.next;\n            }\n            return null\n        },\n\n        set(listener) {\n            if (typeof listener !== \"function\" && !isObject(listener)) {\n                listener = null; // eslint-disable-line no-param-reassign\n            }\n            const listeners = getListeners(this);\n\n            // Traverse to the tail while removing old value.\n            let prev = null;\n            let node = listeners.get(eventName);\n            while (node != null) {\n                if (node.listenerType === ATTRIBUTE) {\n                    // Remove old value.\n                    if (prev !== null) {\n                        prev.next = node.next;\n                    } else if (node.next !== null) {\n                        listeners.set(eventName, node.next);\n                    } else {\n                        listeners.delete(eventName);\n                    }\n                } else {\n                    prev = node;\n                }\n\n                node = node.next;\n            }\n\n            // Add new value.\n            if (listener !== null) {\n                const newNode = {\n                    listener,\n                    listenerType: ATTRIBUTE,\n                    passive: false,\n                    once: false,\n                    next: null,\n                };\n                if (prev === null) {\n                    listeners.set(eventName, newNode);\n                } else {\n                    prev.next = newNode;\n                }\n            }\n        },\n        configurable: true,\n        enumerable: true,\n    }\n}\n\n/**\n * Define an event attribute (e.g. `eventTarget.onclick`).\n * @param {Object} eventTargetPrototype The event target prototype to define an event attrbite.\n * @param {string} eventName The event name to define.\n * @returns {void}\n */\nfunction defineEventAttribute(eventTargetPrototype, eventName) {\n    Object.defineProperty(\n        eventTargetPrototype,\n        `on${eventName}`,\n        defineEventAttributeDescriptor(eventName)\n    );\n}\n\n/**\n * Define a custom EventTarget with event attributes.\n * @param {string[]} eventNames Event names for event attributes.\n * @returns {EventTarget} The custom EventTarget.\n * @private\n */\nfunction defineCustomEventTarget(eventNames) {\n    /** CustomEventTarget */\n    function CustomEventTarget() {\n        EventTarget.call(this);\n    }\n\n    CustomEventTarget.prototype = Object.create(EventTarget.prototype, {\n        constructor: {\n            value: CustomEventTarget,\n            configurable: true,\n            writable: true,\n        },\n    });\n\n    for (let i = 0; i < eventNames.length; ++i) {\n        defineEventAttribute(CustomEventTarget.prototype, eventNames[i]);\n    }\n\n    return CustomEventTarget\n}\n\n/**\n * EventTarget.\n *\n * - This is constructor if no arguments.\n * - This is a function which returns a CustomEventTarget constructor if there are arguments.\n *\n * For example:\n *\n *     class A extends EventTarget {}\n *     class B extends EventTarget(\"message\") {}\n *     class C extends EventTarget(\"message\", \"error\") {}\n *     class D extends EventTarget([\"message\", \"error\"]) {}\n */\nfunction EventTarget() {\n    /*eslint-disable consistent-return */\n    if (this instanceof EventTarget) {\n        listenersMap.set(this, new Map());\n        return\n    }\n    if (arguments.length === 1 && Array.isArray(arguments[0])) {\n        return defineCustomEventTarget(arguments[0])\n    }\n    if (arguments.length > 0) {\n        const types = new Array(arguments.length);\n        for (let i = 0; i < arguments.length; ++i) {\n            types[i] = arguments[i];\n        }\n        return defineCustomEventTarget(types)\n    }\n    throw new TypeError(\"Cannot call a class as a function\")\n    /*eslint-enable consistent-return */\n}\n\n// Should be enumerable, but class methods are not enumerable.\nEventTarget.prototype = {\n    /**\n     * Add a given listener to this event target.\n     * @param {string} eventName The event name to add.\n     * @param {Function} listener The listener to add.\n     * @param {boolean|{capture?:boolean,passive?:boolean,once?:boolean}} [options] The options for this listener.\n     * @returns {void}\n     */\n    addEventListener(eventName, listener, options) {\n        if (listener == null) {\n            return\n        }\n        if (typeof listener !== \"function\" && !isObject(listener)) {\n            throw new TypeError(\"'listener' should be a function or an object.\")\n        }\n\n        const listeners = getListeners(this);\n        const optionsIsObj = isObject(options);\n        const capture = optionsIsObj\n            ? Boolean(options.capture)\n            : Boolean(options);\n        const listenerType = capture ? CAPTURE : BUBBLE;\n        const newNode = {\n            listener,\n            listenerType,\n            passive: optionsIsObj && Boolean(options.passive),\n            once: optionsIsObj && Boolean(options.once),\n            next: null,\n        };\n\n        // Set it as the first node if the first node is null.\n        let node = listeners.get(eventName);\n        if (node === undefined) {\n            listeners.set(eventName, newNode);\n            return\n        }\n\n        // Traverse to the tail while checking duplication..\n        let prev = null;\n        while (node != null) {\n            if (\n                node.listener === listener &&\n                node.listenerType === listenerType\n            ) {\n                // Should ignore duplication.\n                return\n            }\n            prev = node;\n            node = node.next;\n        }\n\n        // Add it.\n        prev.next = newNode;\n    },\n\n    /**\n     * Remove a given listener from this event target.\n     * @param {string} eventName The event name to remove.\n     * @param {Function} listener The listener to remove.\n     * @param {boolean|{capture?:boolean,passive?:boolean,once?:boolean}} [options] The options for this listener.\n     * @returns {void}\n     */\n    removeEventListener(eventName, listener, options) {\n        if (listener == null) {\n            return\n        }\n\n        const listeners = getListeners(this);\n        const capture = isObject(options)\n            ? Boolean(options.capture)\n            : Boolean(options);\n        const listenerType = capture ? CAPTURE : BUBBLE;\n\n        let prev = null;\n        let node = listeners.get(eventName);\n        while (node != null) {\n            if (\n                node.listener === listener &&\n                node.listenerType === listenerType\n            ) {\n                if (prev !== null) {\n                    prev.next = node.next;\n                } else if (node.next !== null) {\n                    listeners.set(eventName, node.next);\n                } else {\n                    listeners.delete(eventName);\n                }\n                return\n            }\n\n            prev = node;\n            node = node.next;\n        }\n    },\n\n    /**\n     * Dispatch a given event.\n     * @param {Event|{type:string}} event The event to dispatch.\n     * @returns {boolean} `false` if canceled.\n     */\n    dispatchEvent(event) {\n        if (event == null || typeof event.type !== \"string\") {\n            throw new TypeError('\"event.type\" should be a string.')\n        }\n\n        // If listeners aren't registered, terminate.\n        const listeners = getListeners(this);\n        const eventName = event.type;\n        let node = listeners.get(eventName);\n        if (node == null) {\n            return true\n        }\n\n        // Since we cannot rewrite several properties, so wrap object.\n        const wrappedEvent = wrapEvent(this, event);\n\n        // This doesn't process capturing phase and bubbling phase.\n        // This isn't participating in a tree.\n        let prev = null;\n        while (node != null) {\n            // Remove this listener if it's once\n            if (node.once) {\n                if (prev !== null) {\n                    prev.next = node.next;\n                } else if (node.next !== null) {\n                    listeners.set(eventName, node.next);\n                } else {\n                    listeners.delete(eventName);\n                }\n            } else {\n                prev = node;\n            }\n\n            // Call this listener\n            setPassiveListener(\n                wrappedEvent,\n                node.passive ? node.listener : null\n            );\n            if (typeof node.listener === \"function\") {\n                try {\n                    node.listener.call(this, wrappedEvent);\n                } catch (err) {\n                    if (\n                        typeof console !== \"undefined\" &&\n                        typeof console.error === \"function\"\n                    ) {\n                        console.error(err);\n                    }\n                }\n            } else if (\n                node.listenerType !== ATTRIBUTE &&\n                typeof node.listener.handleEvent === \"function\"\n            ) {\n                node.listener.handleEvent(wrappedEvent);\n            }\n\n            // Break if `event.stopImmediatePropagation` was called.\n            if (isStopped(wrappedEvent)) {\n                break\n            }\n\n            node = node.next;\n        }\n        setPassiveListener(wrappedEvent, null);\n        setEventPhase(wrappedEvent, 0);\n        setCurrentTarget(wrappedEvent, null);\n\n        return !wrappedEvent.defaultPrevented\n    },\n};\n\n// `constructor` is not enumerable.\nObject.defineProperty(EventTarget.prototype, \"constructor\", {\n    value: EventTarget,\n    configurable: true,\n    writable: true,\n});\n\n// Ensure `eventTarget instanceof window.EventTarget` is `true`.\nif (\n    typeof window !== \"undefined\" &&\n    typeof window.EventTarget !== \"undefined\"\n) {\n    Object.setPrototypeOf(EventTarget.prototype, window.EventTarget.prototype);\n}\n\nexports.defineEventAttribute = defineEventAttribute;\nexports.EventTarget = EventTarget;\nexports[\"default\"] = EventTarget;\n\nmodule.exports = EventTarget\nmodule.exports.EventTarget = module.exports[\"default\"] = EventTarget\nmodule.exports.defineEventAttribute = defineEventAttribute\n//# sourceMappingURL=event-target-shim.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/event-target-shim/dist/event-target-shim.js\n");

/***/ })

};
;