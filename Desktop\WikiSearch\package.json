{"dependencies": {"@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^22.15.21", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "groq": "^3.89.0", "lucide-react": "^0.511.0", "next": "^15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3"}, "name": "wikisearch", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^4.1.7"}}