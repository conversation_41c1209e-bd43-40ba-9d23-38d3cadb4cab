import Groq from "groq-sdk";

// Define types for Wikipedia API responses
export interface WikiSearchResult {
  pageid: number;
  title: string;
  snippet: string;
  url: string;
  thumbnail?: {
    source: string;
    width: number;
    height: number;
  };
}

export interface WikiSearchResponse {
  query: {
    search: WikiSearchResult[];
  };
}

export interface WikiPageContent {
  pageid: number;
  title: string;
  extract: string;
  thumbnail?: {
    source: string;
    width: number;
    height: number;
  };
}

// Cache for AI summaries
const summaryCache = new Map<number, string>();

// Search Wikipedia
export async function searchWikipedia(query: string): Promise<WikiSearchResult[]> {
  if (!query.trim()) return [];

  const url = new URL("https://en.wikipedia.org/w/api.php");
  url.search = new URLSearchParams({
    action: "query",
    list: "search",
    srsearch: query,
    format: "json",
    origin: "*",
    srlimit: "10",
    srprop: "snippet",
  }).toString();

  try {
    const response = await fetch(url.toString());
    const data = await response.json() as WikiSearchResponse;

    // Add URL to each search result
    return data.query.search.map(result => ({
      ...result,
      url: `https://en.wikipedia.org/wiki/${encodeURIComponent(result.title.replace(/ /g, "_"))}`
    }));
  } catch (error) {
    console.error("Error searching Wikipedia:", error);
    return [];
  }
}

// Get Wikipedia page content
export async function getWikipediaPage(pageId: number): Promise<WikiPageContent | null> {
  const url = new URL("https://en.wikipedia.org/w/api.php");
  url.search = new URLSearchParams({
    action: "query",
    pageids: pageId.toString(),
    format: "json",
    origin: "*",
    prop: "extracts|pageimages",
    exintro: "1",
    explaintext: "1",
    pithumbsize: "500",
  }).toString();

  try {
    const response = await fetch(url.toString());
    const data = await response.json();
    const page = data.query.pages[pageId];

    if (!page) return null;

    return {
      pageid: page.pageid,
      title: page.title,
      extract: page.extract,
      thumbnail: page.thumbnail,
    };
  } catch (error) {
    console.error("Error fetching Wikipedia page:", error);
    return null;
  }
}

// Get AI summary using Groq
export async function getAISummary(pageContent: WikiPageContent): Promise<string> {
  // Check cache first
  if (summaryCache.has(pageContent.pageid)) {
    return summaryCache.get(pageContent.pageid)!;
  }

  // Initialize Groq client
  const groq = new Groq({
    apiKey: process.env.GROQ_API,
  });

  try {
    const prompt = `
      Summarize the following Wikipedia article in 3-4 concise paragraphs.
      Focus on the most important information and key facts.

      Article Title: ${pageContent.title}

      Article Content:
      ${pageContent.extract}
    `;

    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      model: "llama3-70b-8192",
    });

    const summary = chatCompletion.choices[0]?.message?.content ||
      "Unable to generate a summary at this time.";

    // Cache the summary
    summaryCache.set(pageContent.pageid, summary);

    return summary;
  } catch (error) {
    console.error("Error generating AI summary:", error);
    return "Unable to generate a summary at this time.";
  }
}