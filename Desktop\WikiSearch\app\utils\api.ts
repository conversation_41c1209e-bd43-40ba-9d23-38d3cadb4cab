import type { WikiSearchResult } from "@/app/lib/api";

// Define SearchParams locally since it's only used in this utility file
interface SearchParams {
  query: string;
  limit?: number;
  offset?: number;
}

// Define SearchResponse locally since it's only used in this utility file
interface SearchResponse {
  results: WikiSearchResult[];
  totalResults: number;
  query: string;
}

/**
 * Searches Wikipedia using the internal API
 * @param params - Search parameters
 * @returns Promise with search results
 */
export async function searchWikipedia({
  query,
  limit = 10,
  offset = 0
}: SearchParams): Promise<WikiSearchResult[]> {
  if (!query.trim()) {
    return [];
  }

  try {
    const searchParams = new URLSearchParams({
      query,
      limit: limit.toString(),
      offset: offset.toString(),
    });

    const response = await fetch(`/api/search?${searchParams.toString()}`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "Failed to search Wikipedia");
    }

    const data: SearchResponse = await response.json();
    return data.results;
  } catch (error) {
    console.error("Error searching Wikipedia:", error);
    throw error;
  }
}