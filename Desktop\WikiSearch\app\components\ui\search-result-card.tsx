import * as React from "react";
import Link from "next/link";
import { ExternalLink } from "lucide-react";
import { cn } from "@/app/utils/cn";
import type { WikiSearchResult } from "@/app/types/search";

interface SearchResultCardProps {
  result: WikiSearchResult;
  className?: string;
}

export function SearchResultCard({ result, className }: SearchResultCardProps) {
  // Create a safe HTML snippet by parsing the HTML from Wikipedia
  const createMarkup = () => {
    return { __html: result.snippet };
  };

  return (
    <div
      className={cn(
        "rounded-lg border bg-card p-4 shadow-sm transition-all hover:shadow-md",
        className
      )}
    >
      <div className="flex items-start justify-between">
        <h3 className="text-lg font-semibold text-card-foreground">
          {result.title}
        </h3>
        <Link
          href={result.url}
          target="_blank"
          rel="noopener noreferrer"
          className="ml-2 inline-flex h-8 w-8 items-center justify-center rounded-md text-muted-foreground hover:bg-accent hover:text-accent-foreground"
          aria-label={`Open ${result.title} in Wikipedia`}
        >
          <ExternalLink className="h-4 w-4" />
        </Link>
      </div>
      <div
        className="mt-2 text-sm text-muted-foreground"
        dangerouslySetInnerHTML={createMarkup()}
      />
      <div className="mt-4 flex items-center text-xs text-muted-foreground">
        <span>Wikipedia • Page ID: {result.pageid}</span>
      </div>
    </div>
  );
}